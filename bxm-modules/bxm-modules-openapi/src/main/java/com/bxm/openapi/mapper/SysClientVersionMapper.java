package com.bxm.openapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.openapi.domain.ExportDTO;
import com.bxm.openapi.domain.SysClientVersion;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 企微信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Mapper
public interface SysClientVersionMapper extends BaseMapper<SysClientVersion>
{
    List<ExportDTO> exportAccountingNotCompleteTotal();

    List<ExportDTO> exportAccountingNotComplete0shenbao();

    List<ExportDTO> exportAccountingNotCompletexiaoguimo();

    List<ExportDTO> exportAccountingNotCompleteyiban();
}
