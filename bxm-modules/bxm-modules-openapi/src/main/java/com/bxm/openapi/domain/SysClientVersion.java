package com.bxm.openapi.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 第三方客户端版本对象 sys_client_version
 * 
 * <AUTHOR>
 * @date 2024-10-08
 */
@Data
@ApiModel("第三方客户端版本对象")
@Accessors(chain = true)
@TableName("sys_client_version")
public class SysClientVersion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 第三方客户端类型 */
    @Excel(name = "第三方客户端类型")
    @TableField("client_type")
    @ApiModelProperty(value = "第三方客户端类型")
    private Byte clientType;

    /** 当前最新版本 */
    @Excel(name = "当前最新版本")
    @TableField("version_code")
    @ApiModelProperty(value = "当前最新版本")
    private String versionCode;

    /** 软件下载地址 */
    @Excel(name = "软件下载地址")
    @TableField("download_url")
    @ApiModelProperty(value = "软件下载地址")
    private String downloadUrl;

    /** 软件更新描述 */
    @Excel(name = "软件更新描述")
    @TableField("update_desc")
    @ApiModelProperty(value = "软件更新描述")
    private String updateDesc;

    /** 是否强制更新，0-否，1-是 */
    @Excel(name = "是否强制更新，0-否，1-是")
    @TableField("is_force_update")
    @ApiModelProperty(value = "是否强制更新，0-否，1-是")
    private Boolean isForceUpdate;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;
}
