<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.BundleDeptMapper">
    
    <resultMap type="com.bxm.system.domain.BundleDept" id="BundleDeptResult">
        <result property="id"    column="id"    />
        <result property="bundleId"    column="bundle_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectBundleDeptVo">
        select id, bundle_id, dept_id, is_del, create_by, create_time, update_by, update_time from sys_bundle_dept
    </sql>

    <select id="selectBundleDeptList" parameterType="com.bxm.system.domain.BundleDept" resultMap="BundleDeptResult">
        <include refid="selectBundleDeptVo"/>
        <where>  
            <if test="bundleId != null "> and bundle_id = #{bundleId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectBundleDeptById" parameterType="Long" resultMap="BundleDeptResult">
        <include refid="selectBundleDeptVo"/>
        where id = #{id}
    </select>
    <select id="selectNotSameBundleIdDeptIds" resultType="java.lang.Long">
        select dept_id
            from sys_bundle_dept
            where bundle_id != #{bundleId} and dept_id in
            <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
                #{deptId}
            </foreach>
    </select>

    <insert id="insertBundleDept" parameterType="com.bxm.system.domain.BundleDept" useGeneratedKeys="true" keyProperty="id">
        insert into sys_bundle_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bundleId != null">bundle_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bundleId != null">#{bundleId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateBundleDept" parameterType="com.bxm.system.domain.BundleDept">
        update sys_bundle_dept
        <trim prefix="SET" suffixOverrides=",">
            <if test="bundleId != null">bundle_id = #{bundleId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateBundleIdByDeptIds">
        update sys_bundle_dept set bundle_id = #{bundleId}
        where dept_id in
        <foreach item="deptId" collection="deptIds" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        and bundle_id != #{bundleId}
    </update>

    <delete id="deleteBundleDeptById" parameterType="Long">
        delete from sys_bundle_dept where id = #{id}
    </delete>

    <delete id="deleteBundleDeptByIds" parameterType="String">
        delete from sys_bundle_dept where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>