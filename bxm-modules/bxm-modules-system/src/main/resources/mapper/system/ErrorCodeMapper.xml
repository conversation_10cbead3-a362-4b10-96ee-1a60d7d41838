<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.ErrorCodeMapper">
    
    <resultMap type="com.bxm.system.domain.ErrorCode" id="ErrorCodeResult">
        <result property="id"    column="id"    />
        <result property="errorCode"    column="error_code"    />
        <result property="errorMsg"    column="error_msg"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectErrorCodeVo">
        select id, error_code, error_msg, create_time, update_time from sys_error_code
    </sql>

    <select id="selectErrorCodeList" parameterType="com.bxm.system.domain.ErrorCode" resultMap="ErrorCodeResult">
        <include refid="selectErrorCodeVo"/>
        <where>  
            <if test="errorCode != null  and errorCode != ''"> and error_code = #{errorCode}</if>
            <if test="errorMsg != null  and errorMsg != ''"> and error_msg = #{errorMsg}</if>
        </where>
    </select>
    
    <select id="selectErrorCodeById" parameterType="Integer" resultMap="ErrorCodeResult">
        <include refid="selectErrorCodeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertErrorCode" parameterType="com.bxm.system.domain.ErrorCode" useGeneratedKeys="true" keyProperty="id">
        insert into sys_error_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="errorCode != null and errorCode != ''">error_code,</if>
            <if test="errorMsg != null and errorMsg != ''">error_msg,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="errorCode != null and errorCode != ''">#{errorCode},</if>
            <if test="errorMsg != null and errorMsg != ''">#{errorMsg},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateErrorCode" parameterType="com.bxm.system.domain.ErrorCode">
        update sys_error_code
        <trim prefix="SET" suffixOverrides=",">
            <if test="errorCode != null and errorCode != ''">error_code = #{errorCode},</if>
            <if test="errorMsg != null and errorMsg != ''">error_msg = #{errorMsg},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteErrorCodeById" parameterType="Integer">
        delete from sys_error_code where id = #{id}
    </delete>

    <delete id="deleteErrorCodeByIds" parameterType="String">
        delete from sys_error_code where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>