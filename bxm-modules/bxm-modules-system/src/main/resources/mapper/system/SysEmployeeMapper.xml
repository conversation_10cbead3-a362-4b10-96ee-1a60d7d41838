<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.system.mapper.SysEmployeeMapper">
    
    <resultMap type="com.bxm.system.api.domain.SysEmployee" id="SysEmployeeResult">
        <result property="employeeId"    column="employee_id"    />
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="topDeptId"    column="top_dept_id"    />
        <result property="employeeName"    column="employee_name"    />
        <result property="employeeMobile"    column="employee_mobile"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysEmployeeVo">
        select employee_id, user_id, dept_id, top_dept_id, employee_name, employee_mobile, create_by, create_time, update_by, update_time, remark from sys_employee
    </sql>

    <select id="selectSysEmployeeList" parameterType="com.bxm.system.api.domain.SysEmployee" resultMap="SysEmployeeResult">
        <include refid="selectSysEmployeeVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="topDeptId != null "> and top_dept_id = #{topDeptId}</if>
            <if test="employeeName != null  and employeeName != ''"> and employee_name like concat('%', #{employeeName}, '%')</if>
            <if test="employeeMobile != null  and exployeeMobile != ''"> and employee_mobile = #{employeeMobile}</if>
        </where>
    </select>
    
    <select id="selectSysEmployeeByEmployeeId" parameterType="Long" resultMap="SysEmployeeResult">
        <include refid="selectSysEmployeeVo"/>
        where employee_id = #{employeeId}
    </select>
    <select id="selectByDeptIdsAndUserIds" resultType="com.bxm.system.api.domain.SysEmployee">
        select
            se.*
            from sys_employee se join sys_dept sd on se.dept_id = sd.dept_id
        <where>
            <if test="deptIds != null and deptIds.size() > 0">
                and se.dept_id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="userId != null">
                and se.user_id = #{userId}
            </if>
        </where>
        order by sd.level,se.employee_id
    </select>

    <insert id="insertSysEmployee" parameterType="com.bxm.system.api.domain.SysEmployee" useGeneratedKeys="true" keyProperty="employeeId">
        insert into sys_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="topDeptId != null">top_dept_id,</if>
            <if test="employeeName != null">employee_name,</if>
            <if test="employeeMobile != null">employee_mobile,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="isLeader != null">is_leader,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="topDeptId != null">#{topDeptId},</if>
            <if test="employeeName != null">#{employeeName},</if>
            <if test="employeeMobile != null">#{employeeMobile},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isLeader != null">#{isLeader}</if>
         </trim>
    </insert>

    <update id="updateSysEmployee" parameterType="com.bxm.system.api.domain.SysEmployee">
        update sys_employee
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="topDeptId != null">top_dept_id = #{topDeptId},</if>
            <if test="employeeName != null">employee_name = #{employeeName},</if>
            <if test="employeeMobile != null">employee_mobile = #{employeeMobile},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isLeader != null">is_leader = #{isLeader},</if>
        </trim>
        where employee_id = #{employeeId}
    </update>

    <delete id="deleteSysEmployeeByEmployeeId" parameterType="Long">
        delete from sys_employee where employee_id = #{employeeId}
    </delete>

    <delete id="deleteSysEmployeeByEmployeeIds" parameterType="String">
        delete from sys_employee where employee_id in 
        <foreach item="employeeId" collection="array" open="(" separator="," close=")">
            #{employeeId}
        </foreach>
    </delete>
</mapper>