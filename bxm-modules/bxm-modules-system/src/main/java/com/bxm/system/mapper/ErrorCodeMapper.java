package com.bxm.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.system.domain.ErrorCode;

/**
 * 错误信息配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Mapper
public interface ErrorCodeMapper extends BaseMapper<ErrorCode>
{
    /**
     * 查询错误信息配置
     * 
     * @param id 错误信息配置主键
     * @return 错误信息配置
     */
    public ErrorCode selectErrorCodeById(Integer id);

    /**
     * 查询错误信息配置列表
     * 
     * @param errorCode 错误信息配置
     * @return 错误信息配置集合
     */
    public List<ErrorCode> selectErrorCodeList(ErrorCode errorCode);

    /**
     * 新增错误信息配置
     * 
     * @param errorCode 错误信息配置
     * @return 结果
     */
    public int insertErrorCode(ErrorCode errorCode);

    /**
     * 修改错误信息配置
     * 
     * @param errorCode 错误信息配置
     * @return 结果
     */
    public int updateErrorCode(ErrorCode errorCode);

    /**
     * 删除错误信息配置
     * 
     * @param id 错误信息配置主键
     * @return 结果
     */
    public int deleteErrorCodeById(Integer id);

    /**
     * 批量删除错误信息配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteErrorCodeByIds(Integer[] ids);
}
