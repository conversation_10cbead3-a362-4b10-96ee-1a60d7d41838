package com.bxm.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.DeptAccountBalanceDetailSource;
import com.bxm.common.core.enums.DeptAccountBalanceDetailStatus;
import com.bxm.common.core.enums.IncomeType;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.system.api.domain.CancelBalanceDetailVO;
import com.bxm.system.api.domain.ConfirmBalanceDetailVO;
import com.bxm.system.api.domain.RemoteDeptAccountBalanceDetail;
import com.bxm.system.domain.SysDeptAccountBalanceDetail;
import com.bxm.system.domain.SysDeptAccountBalanceDetailFile;
import com.bxm.system.domain.dto.DeptAccountBalanceDetailDTO;
import com.bxm.system.domain.vo.DeptAccountBalanceAllotVO;
import com.bxm.system.mapper.SysDeptAccountBalanceDetailMapper;
import com.bxm.system.mapper.SysDeptMapper;
import com.bxm.system.service.FileService;
import com.bxm.system.service.ISysDeptAccountBalanceDetailFileService;
import com.bxm.system.service.ISysDeptAccountBalanceDetailService;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 业务公司余额变动明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
@Service
public class SysDeptAccountBalanceDetailServiceImpl extends ServiceImpl<SysDeptAccountBalanceDetailMapper, SysDeptAccountBalanceDetail> implements ISysDeptAccountBalanceDetailService
{
    @Autowired
    private SysDeptAccountBalanceDetailMapper sysDeptAccountBalanceDetailMapper;

    @Autowired
    private ISysDeptAccountBalanceDetailFileService sysDeptAccountBalanceDetailFileService;

    @Autowired
    private FileService fileService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询业务公司余额变动明细
     * 
     * @param id 业务公司余额变动明细主键
     * @return 业务公司余额变动明细
     */
    @Override
    public SysDeptAccountBalanceDetail selectSysDeptAccountBalanceDetailById(Long id)
    {
        return sysDeptAccountBalanceDetailMapper.selectSysDeptAccountBalanceDetailById(id);
    }

    /**
     * 查询业务公司余额变动明细列表
     * 
     * @param sysDeptAccountBalanceDetail 业务公司余额变动明细
     * @return 业务公司余额变动明细
     */
    @Override
    public List<SysDeptAccountBalanceDetail> selectSysDeptAccountBalanceDetailList(SysDeptAccountBalanceDetail sysDeptAccountBalanceDetail)
    {
        return sysDeptAccountBalanceDetailMapper.selectSysDeptAccountBalanceDetailList(sysDeptAccountBalanceDetail);
    }

    /**
     * 新增业务公司余额变动明细
     * 
     * @param sysDeptAccountBalanceDetail 业务公司余额变动明细
     * @return 结果
     */
    @Override
    public int insertSysDeptAccountBalanceDetail(SysDeptAccountBalanceDetail sysDeptAccountBalanceDetail)
    {
        sysDeptAccountBalanceDetail.setCreateTime(DateUtils.getNowDate());
        return sysDeptAccountBalanceDetailMapper.insertSysDeptAccountBalanceDetail(sysDeptAccountBalanceDetail);
    }

    /**
     * 修改业务公司余额变动明细
     * 
     * @param sysDeptAccountBalanceDetail 业务公司余额变动明细
     * @return 结果
     */
    @Override
    public int updateSysDeptAccountBalanceDetail(SysDeptAccountBalanceDetail sysDeptAccountBalanceDetail)
    {
        sysDeptAccountBalanceDetail.setUpdateTime(DateUtils.getNowDate());
        return sysDeptAccountBalanceDetailMapper.updateSysDeptAccountBalanceDetail(sysDeptAccountBalanceDetail);
    }

    /**
     * 批量删除业务公司余额变动明细
     * 
     * @param ids 需要删除的业务公司余额变动明细主键
     * @return 结果
     */
    @Override
    public int deleteSysDeptAccountBalanceDetailByIds(Long[] ids)
    {
        return sysDeptAccountBalanceDetailMapper.deleteSysDeptAccountBalanceDetailByIds(ids);
    }

    /**
     * 删除业务公司余额变动明细信息
     * 
     * @param id 业务公司余额变动明细主键
     * @return 结果
     */
    @Override
    public int deleteSysDeptAccountBalanceDetailById(Long id)
    {
        return sysDeptAccountBalanceDetailMapper.deleteSysDeptAccountBalanceDetailById(id);
    }

    @Override
    public void createBalanceDetail(DeptAccountBalanceAllotVO vo, String employeeName, Integer fileType) {
        SysDeptAccountBalanceDetail detail = new SysDeptAccountBalanceDetail().setBusinessDeptId(vo.getBusinessDeptId())
                .setSourceType(DeptAccountBalanceDetailSource.allotTypeToSource(vo.getAllotType()).getCode())
                .setIncomeType(IncomeType.allotTypeToIncomeType(vo.getAllotType()).getCode())
                .setChangeAmount(vo.getAmount())
                .setStatus(DeptAccountBalanceDetailStatus.CONFIRMED.getCode())
                .setRemark(vo.getRemark());
        detail.setCreateBy(employeeName);
        save(detail);
        if (!ObjectUtils.isEmpty(vo.getFiles())) {
            sysDeptAccountBalanceDetailFileService.saveBatch(vo.getFiles().stream().map(file -> new SysDeptAccountBalanceDetailFile().setBalanceDetailId(detail.getId()).setFileUrl(file.getFileUrl())
                    .setFileName(file.getFileName()).setFileType(fileType)).collect(Collectors.toList()));
        }
    }

    @Override
    public IPage<DeptAccountBalanceDetailDTO> accountBalanceDetailList(Long businessDeptId, Integer pageNum, Integer pageSize) {
        IPage<DeptAccountBalanceDetailDTO> result = new Page<>();
        IPage<SysDeptAccountBalanceDetail> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<SysDeptAccountBalanceDetail>()
                .eq(SysDeptAccountBalanceDetail::getBusinessDeptId, businessDeptId).orderByDesc(SysDeptAccountBalanceDetail::getCreateTime));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            Map<Long, List<SysDeptAccountBalanceDetailFile>> fileMap = sysDeptAccountBalanceDetailFileService.selectBatchByDetailIdAndFileType(iPage.getRecords().stream().map(SysDeptAccountBalanceDetail::getId).collect(Collectors.toList()), 1)
                    .stream().collect(Collectors.groupingBy(SysDeptAccountBalanceDetailFile::getBalanceDetailId));
            result.setRecords(iPage.getRecords().stream().map(row -> DeptAccountBalanceDetailDTO.builder()
                    .id(row.getId())
                    .createTime(row.getCreateTime())
                    .sourceType(row.getSourceType())
                    .sourceTypeName(DeptAccountBalanceDetailSource.getByCode(row.getSourceType()).getDesc())
                    .businessNo(row.getBusinessNo())
                    .incomeType(IncomeType.getByCode(row.getIncomeType()).getDesc())
                    .changeAmount(row.getChangeAmount().compareTo(BigDecimal.ZERO) == 0 ? "0" : ((Objects.equals(row.getIncomeType(), IncomeType.INCOME.getCode()) ? "+" : "-") + row.getChangeAmount().stripTrailingZeros().toPlainString()))
                    .status(row.getStatus())
                    .statusName(DeptAccountBalanceDetailStatus.getByCode(row.getStatus()).getDesc())
                    .remark(row.getRemark())
                    .fileCount(fileMap.getOrDefault(row.getId(), Lists.newArrayList())
                            .size()).build()).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public List<CommonFileVO> getBalanceDetailFiles(Long id) {
        if (Objects.isNull(id)) {
            return Collections.emptyList();
        }
        return sysDeptAccountBalanceDetailFileService.selectByDetailIdAndFileType(id, 1)
                .stream().map(f -> CommonFileVO.builder()
                .fileName(f.getFileName())
                .fileUrl(f.getFileUrl())
                .fullFileUrl(fileService.getFullFileUrl(f.getFileUrl())).build())
                .collect(Collectors.toList());
    }

    @Override
    public void createBalanceDetail(RemoteDeptAccountBalanceDetail detail) {
        SysDeptAccountBalanceDetail sysDeptAccountBalanceDetail = new SysDeptAccountBalanceDetail();
        BeanUtils.copyProperties(detail, sysDeptAccountBalanceDetail);
        save(sysDeptAccountBalanceDetail);
        BigDecimal changeBalanceAmount = Objects.equals(detail.getIncomeType(), IncomeType.INCOME.getCode()) ? detail.getChangeAmount() : detail.getChangeAmount().negate();
        BigDecimal frozeBalanceAmount = Objects.equals(detail.getStatus(), DeptAccountBalanceDetailStatus.UNCONFIRMED.getCode()) && Objects.equals(IncomeType.EXPENDITURE.getCode(), detail.getIncomeType()) ? detail.getChangeAmount() : BigDecimal.ZERO;
        sysDeptMapper.updateAccountBalanceToFrozeBalance(detail.getBusinessDeptId(), changeBalanceAmount, frozeBalanceAmount);
    }

    @Override
    @Transactional
    public void batchCreateBalanceDetail(List<RemoteDeptAccountBalanceDetail> details) {
        if (ObjectUtils.isEmpty(details)) {
            return;
        }
        saveBatch(details.stream().map(detail -> {
            SysDeptAccountBalanceDetail sysDeptAccountBalanceDetail = new SysDeptAccountBalanceDetail();
            BeanUtils.copyProperties(detail, sysDeptAccountBalanceDetail);
            return sysDeptAccountBalanceDetail;
        }).collect(Collectors.toList()));
        Map<Long, List<RemoteDeptAccountBalanceDetail>> detailMap = details.stream().collect(Collectors.groupingBy(RemoteDeptAccountBalanceDetail::getBusinessDeptId));
        detailMap.forEach((businessDeptId, detailList) -> {
            BigDecimal changeBalanceAmount = BigDecimal.ZERO;
            BigDecimal frozeBalanceAmount = BigDecimal.ZERO;
            for (RemoteDeptAccountBalanceDetail detail : detailList) {
                changeBalanceAmount = Objects.equals(detail.getIncomeType(), IncomeType.INCOME.getCode()) ? changeBalanceAmount.add(detail.getChangeAmount()) : changeBalanceAmount.subtract(detail.getChangeAmount());
                if (Objects.equals(detail.getStatus(), DeptAccountBalanceDetailStatus.UNCONFIRMED.getCode()) && Objects.equals(IncomeType.EXPENDITURE.getCode(), detail.getIncomeType())) {
                    frozeBalanceAmount = frozeBalanceAmount.add(detail.getChangeAmount());
                }
            }
            sysDeptMapper.updateAccountBalanceToFrozeBalance(businessDeptId, changeBalanceAmount, frozeBalanceAmount);
        });
    }

    @Override
    @Transactional
    public void batchCancelBalanceDetail(CancelBalanceDetailVO vo) {
        if (ObjectUtils.isEmpty(vo.getBusinessIds())) {
            return;
        }
        List<SysDeptAccountBalanceDetail> details = list(new LambdaQueryWrapper<SysDeptAccountBalanceDetail>()
                .eq(SysDeptAccountBalanceDetail::getBusinessType, vo.getBusinessType())
                .eq(SysDeptAccountBalanceDetail::getSourceType, vo.getSourceType())
                .in(SysDeptAccountBalanceDetail::getBusinessId, vo.getBusinessIds())
                .eq(SysDeptAccountBalanceDetail::getStatus, DeptAccountBalanceDetailStatus.UNCONFIRMED.getCode()));
        if (ObjectUtils.isEmpty(details)) {
            return;
        }
        updateBatchById(details.stream().map(detail -> new SysDeptAccountBalanceDetail().setId(detail.getId()).setStatus(DeptAccountBalanceDetailStatus.CANCELED.getCode())).collect(Collectors.toList()));
        Map<Long, List<SysDeptAccountBalanceDetail>> detailMap = details.stream().collect(Collectors.groupingBy(SysDeptAccountBalanceDetail::getBusinessDeptId));
        detailMap.forEach((businessDeptId, detailList) -> {
            BigDecimal changeBalanceAmount = BigDecimal.ZERO;
            for (SysDeptAccountBalanceDetail detail : detailList) {
                changeBalanceAmount = changeBalanceAmount.add(detail.getChangeAmount());
            }
            sysDeptMapper.updateFrozeBalanceToAccountBalance(businessDeptId, changeBalanceAmount);
        });
    }

    @Override
    @Transactional
    public void remoteBatchConfirmBalanceDetail(ConfirmBalanceDetailVO vo) {
        if (ObjectUtils.isEmpty(vo.getBusinessIds())) {
            return;
        }
        List<SysDeptAccountBalanceDetail> details = list(new LambdaQueryWrapper<SysDeptAccountBalanceDetail>()
                .eq(SysDeptAccountBalanceDetail::getBusinessType, vo.getBusinessType())
                .eq(SysDeptAccountBalanceDetail::getSourceType, vo.getSourceType())
                .in(SysDeptAccountBalanceDetail::getBusinessId, vo.getBusinessIds())
                .eq(SysDeptAccountBalanceDetail::getStatus, DeptAccountBalanceDetailStatus.UNCONFIRMED.getCode()));
        if (ObjectUtils.isEmpty(details)) {
            return;
        }
        updateBatchById(details.stream().map(detail -> new SysDeptAccountBalanceDetail().setId(detail.getId()).setStatus(DeptAccountBalanceDetailStatus.CONFIRMED.getCode())).collect(Collectors.toList()));
        Map<Long, List<SysDeptAccountBalanceDetail>> detailMap = details.stream().collect(Collectors.groupingBy(SysDeptAccountBalanceDetail::getBusinessDeptId));
        detailMap.forEach((businessDeptId, detailList) -> {
            BigDecimal changeBalanceAmount = BigDecimal.ZERO;
            for (SysDeptAccountBalanceDetail detail : detailList) {
                changeBalanceAmount = changeBalanceAmount.add(detail.getChangeAmount());
            }
            sysDeptMapper.updateFrozeBalance(businessDeptId, changeBalanceAmount);
        });
    }
}
