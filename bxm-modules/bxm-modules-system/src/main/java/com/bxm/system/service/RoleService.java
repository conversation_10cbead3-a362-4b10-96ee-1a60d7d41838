package com.bxm.system.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.enums.DeptTypeEnum;
import com.bxm.common.core.enums.RoleTypeEnum;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysRole;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.domain.*;
import com.bxm.system.domain.dto.role.RoleDTO;
import com.bxm.system.domain.dto.role.RoleDetailDTO;
import com.bxm.system.domain.vo.role.RoleAddVO;
import com.bxm.system.domain.vo.role.RoleModifyVO;
import com.bxm.system.domain.vo.role.RoleUpdateMenuVO;
import com.bxm.system.mapper.*;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class RoleService {

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final int ROLE_KEY_LENGTH = 10;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Autowired
    private IBusinessLogService businessLogService;

    @Autowired
    private RoleDeptTypeMapper roleDeptTypeMapper;

    @Autowired
    private IRoleDeptTypeService roleDeptTypeService;

    @Autowired
    private SysMenuMapper sysMenuMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    public IPage<RoleDTO> roleList(String roleName, Integer pageNum, Integer pageSize) {
        return sysRoleService.rolePageList(roleName, pageNum, pageSize);
    }

    public RoleDetailDTO detail(Long roleId) {
        SysRole sysRole = sysRoleService.getById(roleId);
        if (Objects.isNull(sysRole) || !"0".equals(sysRole.getDelFlag())) {
            throw new ServiceException("角色不存在");
        }
        List<Integer> deptTypes = roleDeptTypeMapper.selectList(new LambdaQueryWrapper<RoleDeptType>()
                        .eq(RoleDeptType::getRoleId, roleId).select(RoleDeptType::getDeptType))
                .stream().map(RoleDeptType::getDeptType).collect(Collectors.toList());
        return RoleDetailDTO.builder()
                .id(sysRole.getRoleId())
                .belongDeptId(sysRole.getBelongDeptId())
                .deptTypes(deptTypes)
                .menuIds(getMenuIdsByRoleId(roleId))
                .remark(sysRole.getRemark())
                .roleKey(sysRole.getRoleKey())
                .roleName(sysRole.getRoleName())
                .roleType(sysRole.getRoleType())
                .build();
    }

    @Transactional
    public void addRole(RoleAddVO vo, Long deptId) {
        if (checkRoleNameExists(vo.getRoleName(), null)) {
            throw new ServiceException("角色名已存在");
        }
        String roleKey;
        if (Objects.equals(vo.getRoleType(), RoleTypeEnum.SYSTEM.getCode())) {
            roleKey = vo.getRoleKey();
        } else {
            roleKey = generateUniqueRoleKey();
        }
        if (checkRoleKeyExists(roleKey, null)) {
            throw new ServiceException("角色code已存在");
        }
        SysRole sysRole = new SysRole();
        sysRole.setRoleName(vo.getRoleName());
        sysRole.setRoleKey(roleKey);
        sysRole.setRoleSort(0);
        sysRole.setDataScope("1");
        sysRole.setMenuCheckStrictly(false);
        sysRole.setDeptCheckStrictly(true);
        sysRole.setStatus("0");
        sysRole.setDelFlag("0");
        sysRole.setBelongDeptId(vo.getBelongDeptId());
        sysRole.setRoleType(vo.getRoleType());

        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        sysRole.setCreateBy(currentUser.getNickName());
        sysRole.setRemark(vo.getRemark());
        sysRoleService.save(sysRole);

        if (!ObjectUtils.isEmpty(vo.getDeptTypes())) {
            roleDeptTypeService.saveBatch(vo.getDeptTypes().stream().map(row -> new RoleDeptType().setRoleId(sysRole.getRoleId())
                    .setDeptType(row)).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(vo.getMenuIds())) {
            sysRoleMenuMapper.saveRoleMenuBatch(sysRole.getRoleId(), vo.getMenuIds());
        }

        saveBusinessLog(sysRole.getRoleId(), currentUser, "新建", deptId, null, vo.getRemark());
    }

    @Transactional
    public void modifyRole(RoleModifyVO vo, Long deptId) {
        SysRole sysRole = selectById(vo.getId());
        if (Objects.isNull(sysRole)) {
            throw new ServiceException("角色不存在");
        }
        if (checkRoleNameExists(vo.getRoleName(), vo.getId())) {
            throw new ServiceException("角色名已存在");
        }
        String roleKey;
        if (Objects.equals(vo.getRoleType(), RoleTypeEnum.SYSTEM.getCode())) {
            roleKey = vo.getRoleKey();
        } else {
            roleKey = sysRole.getRoleKey();
        }
        if (checkRoleKeyExists(roleKey, vo.getId())) {
            throw new ServiceException("角色code已存在");
        }
        List<SysMenu> oldMenus = selectMenusByRoleId(vo.getId());
        List<SysMenu> newMenus = sysMenuMapper.selectBatchIds(vo.getMenuIds());
        List<RoleDeptType> oldDeptTypes = roleDeptTypeMapper.selectList(new LambdaQueryWrapper<RoleDeptType>()
                        .eq(RoleDeptType::getRoleId, vo.getId()));
        SysRole update = new SysRole();
        update.setRoleId(vo.getId());
        update.setRoleName(vo.getRoleName());
        update.setRoleKey(roleKey);

        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        update.setUpdateBy(currentUser.getNickName());
        update.setRemark(vo.getRemark());
        sysRoleService.updateById(update);

        if (!ObjectUtils.isEmpty(oldDeptTypes)) {
            roleDeptTypeService.removeByIds(oldDeptTypes.stream().map(RoleDeptType::getId).collect(Collectors.toList()));
        }
        if (!ObjectUtils.isEmpty(vo.getDeptTypes())) {
            roleDeptTypeService.saveBatch(vo.getDeptTypes().stream().map(row -> new RoleDeptType().setRoleId(sysRole.getRoleId())
                    .setDeptType(row)).collect(Collectors.toList()));
        }
        sysRoleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>()
                .eq(SysRoleMenu::getRoleId, vo.getId()));
        if (!ObjectUtils.isEmpty(vo.getMenuIds())) {
            sysRoleMenuMapper.saveRoleMenuBatch(sysRole.getRoleId(), vo.getMenuIds());
        }
        Map<String, Object> operContent = buildModifyRoleOperContent(sysRole, vo, oldDeptTypes, roleKey, oldMenus, newMenus);
        saveBusinessLog(sysRole.getRoleId(), currentUser, "编辑", deptId, operContent, null);
    }

    @Transactional
    public void deleteRole(Long roleId, Long deptId) {
        SysRole sysRole = selectById(roleId);
        if (Objects.isNull(sysRole)) {
            throw new ServiceException("角色不存在");
        }
        if (sysUserRoleMapper.selectCount(new LambdaQueryWrapper<SysUserRole>()
                .eq(SysUserRole::getRoleId, roleId)) > 0) {
            throw new ServiceException("该角色已被用户关联，无法删除");
        }
        SysRole update = new SysRole();
        update.setRoleId(roleId);
        update.setDelFlag("2");
        sysRoleService.updateById(update);

        sysRoleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>()
                .eq(SysRoleMenu::getRoleId, roleId));

        roleDeptTypeService.remove(new LambdaQueryWrapper<RoleDeptType>()
                .eq(RoleDeptType::getRoleId, roleId));
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        saveBusinessLog(sysRole.getRoleId(), currentUser, "删除", deptId, null, null);
    }

    @Transactional
    public void addRoleMenu(RoleUpdateMenuVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要操作的角色");
        }
        if (ObjectUtils.isEmpty(vo.getMenuIds())) {
            throw new ServiceException("请选择要添加的权限");
        }
        List<SysMenu> menus = sysMenuMapper.selectBatchIds(vo.getMenuIds());
        if (ObjectUtils.isEmpty(menus)) {
            throw new ServiceException("权限不存在");
        }
        List<SysRole> roles = sysRoleService.list(new LambdaQueryWrapper<SysRole>().eq(SysRole::getDelFlag, "0")
                .in(SysRole::getRoleId, vo.getIds()));
        if (ObjectUtils.isEmpty(roles)) {
            return;
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        Map<Long, List<SysRoleMenu>> roleMenuMap = sysRoleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenu>().in(SysRoleMenu::getRoleId, roles.stream().map(SysRole::getRoleId).collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(SysRoleMenu::getRoleId));
        roles.forEach(role -> createRoleMenu(role, roleMenuMap, menus, currentUser, deptId));
    }

    @Transactional
    public void deleteRoleMenu(RoleUpdateMenuVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getIds())) {
            throw new ServiceException("请选择要操作的角色");
        }
        if (ObjectUtils.isEmpty(vo.getMenuIds())) {
            throw new ServiceException("请选择要删除的权限");
        }
        List<SysMenu> menus = sysMenuMapper.selectBatchIds(vo.getMenuIds());
        if (ObjectUtils.isEmpty(menus)) {
            throw new ServiceException("权限不存在");
        }
        List<SysRole> roles = sysRoleService.list(new LambdaQueryWrapper<SysRole>().eq(SysRole::getDelFlag, "0")
                .in(SysRole::getRoleId, vo.getIds()));
        if (ObjectUtils.isEmpty(roles)) {
            return;
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        Map<Long, List<SysRoleMenu>> roleMenuMap = sysRoleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenu>().in(SysRoleMenu::getRoleId, roles.stream().map(SysRole::getRoleId).collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(SysRoleMenu::getRoleId));
        roles.forEach(role -> removeRoleMenu(role, roleMenuMap, menus, currentUser, deptId));
    }

    public List<RoleDTO> roleSelectList(Long deptId) {
        SysDept sysDept = sysDeptMapper.selectById(deptId);
        if (Objects.isNull(sysDept) || !"0".equals(sysDept.getDelFlag())) {
            throw new ServiceException("当前公司不存在");
        }
        return sysRoleService.selectRoleSelectList(sysDept);
    }

    private void createRoleMenu(SysRole role, Map<Long, List<SysRoleMenu>> roleMenuMap, List<SysMenu> menus, SysUser currentUser, Long deptId) {
        List<Long> oldMenuIds = roleMenuMap.getOrDefault(role.getRoleId(), Lists.newArrayList())
                .stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        List<Long> newMenuIds = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
        List<Long> addMenuIds = newMenuIds.stream().filter(menuId -> !oldMenuIds.contains(menuId)).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(addMenuIds)) {
            return;
        }
        sysRoleMenuMapper.saveRoleMenuBatch(role.getRoleId(), addMenuIds);
        Map<String, Object> operContent = new LinkedHashMap<>();
        String addedMenus = menus.stream()
                .filter(menu -> addMenuIds.contains(menu.getMenuId()))
                .map(menu -> menu.getMenuId() + menu.getMenuName())
                .collect(Collectors.joining("，"));
        operContent.put("增加权限", addedMenus);
        saveBusinessLog(role.getRoleId(), currentUser, "批量增加权限", deptId, operContent, null);
    }

    private void removeRoleMenu(SysRole sysRole, Map<Long, List<SysRoleMenu>> roleMenuMap, List<SysMenu> menus, SysUser currentUser, Long deptId) {
        List<Long> oldMenuIds = roleMenuMap.getOrDefault(sysRole.getRoleId(), Lists.newArrayList())
                .stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        List<Long> newMenuIds = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
        List<Long> removeMenuIds = newMenuIds.stream().filter(oldMenuIds::contains).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(removeMenuIds)) {
            return;
        }
        sysRoleMenuMapper.deleteByRoleIdAndMenuIds(sysRole.getRoleId(), removeMenuIds);
        Map<String, Object> operContent = new LinkedHashMap<>();
        String removedMenus = menus.stream()
                .filter(menu -> removeMenuIds.contains(menu.getMenuId()))
                .map(menu -> menu.getMenuId() + menu.getMenuName())
                .collect(Collectors.joining("，"));
        operContent.put("删除权限", removedMenus);
        saveBusinessLog(sysRole.getRoleId(), currentUser, "批量删除权限", deptId, operContent, null);
    }

    private Map<String, Object> buildModifyRoleOperContent(SysRole sysRole, RoleModifyVO vo, List<RoleDeptType> oldDeptTypes, String roleKey, List<SysMenu> oldMenus, List<SysMenu> newMenus) {
        Map<String, Object> operContent = new LinkedHashMap<>();
        if (!Objects.equals(sysRole.getRoleName(), vo.getRoleName())) {
            operContent.put("角色名", vo.getRoleName());
        }
        if (!Objects.equals(sysRole.getRoleKey(), roleKey)) {
            operContent.put("角色code", roleKey);
        }
        List<Integer> newDeptTypes = ObjectUtils.isEmpty(vo.getDeptTypes()) ? Lists.newArrayList() :
                vo.getDeptTypes().stream().sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());
        List<Integer> oldDeptTypesList = ObjectUtils.isEmpty(oldDeptTypes) ? Lists.newArrayList() :
                oldDeptTypes.stream().map(RoleDeptType::getDeptType).sorted(Comparator.comparing(Integer::intValue)).collect(Collectors.toList());
        if (!newDeptTypes.equals(oldDeptTypesList)) {
            if (ObjectUtils.isEmpty(newDeptTypes)) {
                operContent.put("可用组织类型", "清空");
            } else {
                operContent.put("可用组织类型", newDeptTypes.stream().map(DeptTypeEnum::getNameByCode).collect(Collectors.joining("，")));

            }
        }
        if (!Objects.equals(sysRole.getRemark(), vo.getRemark())) {
            if (StringUtils.isEmpty(vo.getRemark())) {
                operContent.put("备注", "清空");
            } else {
                operContent.put("备注", vo.getRemark());
            }
        }
        // 获取旧菜单和新菜单的 ID 集合
        Set<Long> oldMenuIds = oldMenus.stream().map(SysMenu::getMenuId).collect(Collectors.toSet());
        Set<Long> newMenuIds = newMenus.stream().map(SysMenu::getMenuId).collect(Collectors.toSet());

        // 找出新增的菜单 ID 和删除的菜单 ID
        Set<Long> addedMenuIds = new HashSet<>(newMenuIds);
        addedMenuIds.removeAll(oldMenuIds);

        Set<Long> removedMenuIds = new HashSet<>(oldMenuIds);
        removedMenuIds.removeAll(newMenuIds);

        // 构建新增权限信息
        if (!addedMenuIds.isEmpty()) {
            String addedMenus = newMenus.stream()
                    .filter(menu -> addedMenuIds.contains(menu.getMenuId()))
                    .map(menu -> menu.getMenuId() + menu.getMenuName())
                    .collect(Collectors.joining("，"));
            operContent.put("增加权限", addedMenus);
        }

        // 构建删除权限信息
        if (!removedMenuIds.isEmpty()) {
            String removedMenus = oldMenus.stream()
                    .filter(menu -> removedMenuIds.contains(menu.getMenuId()))
                    .map(menu -> menu.getMenuId() + menu.getMenuName())
                    .collect(Collectors.joining("，"));
            operContent.put("删除权限", removedMenus);
        }

        return operContent;
    }

    private SysRole selectById(Long roleId) {
        if (Objects.isNull(roleId)) {
            return null;
        }
        return sysRoleService.getOne(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleId, roleId)
                .eq(SysRole::getDelFlag, "0"));
    }

    private boolean checkRoleNameExists(String roleName, Long roleId) {
        return sysRoleService.count(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleName, roleName)
                .eq(SysRole::getDelFlag, "0")
                .ne(!Objects.isNull(roleId), SysRole::getRoleId, roleId)) > 0;
    }

    private boolean checkRoleKeyExists(String roleKey, Long roleId) {
        return sysRoleService.count(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getRoleKey, roleKey)
                .eq(SysRole::getDelFlag, "0")
                .ne(!Objects.isNull(roleId), SysRole::getRoleId, roleId)) > 0;
    }

    /**
     * 生成唯一的 role_key
     *
     * @return 唯一的 10 位随机字符串
     */
    public String generateUniqueRoleKey() {
        // 生成随机字符串
        String roleKey;
        do {
            roleKey = generateRandomString(ROLE_KEY_LENGTH);
        } while (!isUniqueRoleKey(roleKey));

        return roleKey;
    }

    /**
     * 检查 role_key 是否唯一
     *
     * @param roleKey 要检查的 role_key
     * @return 如果 role_key 唯一，返回 true；否则返回 false
     */
    private boolean isUniqueRoleKey(String roleKey) {
        return !existsRoleKeyInDatabase(roleKey);
    }

    /**
     * 生成指定长度的随机字符串
     *
     * @param length 字符串长度
     * @return 随机字符串
     */
    private String generateRandomString(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            sb.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }

        return sb.toString();
    }

    /**
     * 检查数据库中是否存在相同的 role_key 且 del_flag='0'
     *
     * @param roleKey 要检查的 role_key
     * @return 如果存在相同的 role_key，返回 true；否则返回 false
     */
    private boolean existsRoleKeyInDatabase(String roleKey) {
        return checkRoleKeyExists(roleKey, null);
    }

    private List<Long> getMenuIdsByRoleId(Long roleId) {
        if (Objects.isNull(roleId)) {
            return Collections.emptyList();
        }
        return sysRoleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenu>()
                .eq(SysRoleMenu::getRoleId, roleId)
                .select(SysRoleMenu::getMenuId))
                .stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
    }

    private void saveBusinessLog(Long businessId, SysUser currentUser, String operType, Long deptId, Map<String, Object> operContent, String operRemark) {
        BusinessLog businessLog = new BusinessLog().setBusinessId(businessId)
                .setBusinessType(BusinessLogBusinessType.ROLE.getCode())
                .setOperName(currentUser.getNickName())
                .setOperType(operType)
                .setOperUserId(currentUser.getUserId())
                .setDeptName("")
                .setDeptId(deptId)
                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                .setOperRemark(operRemark);

        businessLogService.insertBusinessLog(businessLog);
    }

    private List<SysMenu> selectMenusByRoleId(Long roleId) {
        if (Objects.isNull(roleId)) {
            return Collections.emptyList();
        }
        List<Long> menuIds = sysRoleMenuMapper.selectList(new LambdaQueryWrapper<SysRoleMenu>()
                .eq(SysRoleMenu::getRoleId, roleId).select(SysRoleMenu::getMenuId))
                .stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(menuIds)) {
            return Collections.emptyList();
        }
        return sysMenuMapper.selectBatchIds(menuIds);
    }
}
