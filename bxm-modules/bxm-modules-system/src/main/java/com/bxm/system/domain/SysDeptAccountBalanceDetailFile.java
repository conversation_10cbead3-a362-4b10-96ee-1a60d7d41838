package com.bxm.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务公司余额变动明细附件对象 sys_dept_account_balance_detail_file
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
@Data
@ApiModel("业务公司余额变动明细附件对象")
@Accessors(chain = true)
@TableName("sys_dept_account_balance_detail_file")
public class SysDeptAccountBalanceDetailFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 变动明细id */
    @Excel(name = "变动明细id")
    @TableField("balance_detail_id")
    @ApiModelProperty(value = "变动明细id")
    private Long balanceDetailId;

    /** url */
    @Excel(name = "url")
    @TableField("file_url")
    @ApiModelProperty(value = "url")
    private String fileUrl;

    /** 文件名 */
    @Excel(name = "文件名")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名")
    private String fileName;

    /** 文件类型，1-调拨附件 */
    @Excel(name = "文件类型，1-调拨附件")
    @TableField("file_type")
    @ApiModelProperty(value = "文件类型，1-调拨附件")
    private Integer fileType;

}
