package com.bxm.system.controller;

import com.bxm.common.core.domain.Result;
import com.bxm.system.domain.dto.CityInfoDTO;
import com.bxm.system.service.ICityInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/city")
@Api(tags = "城市信息接口")
public class CityInfoController {

    @Autowired
    private ICityInfoService cityInfoService;


    @GetMapping("/cityInfo")
    @ApiOperation("获取省市信息")
    public Result<List<CityInfoDTO>> cityInfo() {
        return Result.ok(cityInfoService.cityInfo());
    }
}
