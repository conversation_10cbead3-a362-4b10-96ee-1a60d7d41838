package com.bxm.system.domain.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CheckUserRepeatDTO {

    @ApiModelProperty("校验结果，0-无重复，1-有重复")
    private Integer checkResult;

    @ApiModelProperty("重复文案")
    private String checkMsg;
}
