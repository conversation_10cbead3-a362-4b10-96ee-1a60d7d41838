package com.bxm.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.web.domain.AjaxResult;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.domain.dto.companyWechat.WechatFileResultDTO;
import com.bxm.system.domain.vo.companyWechat.SendMessageVO;
import com.bxm.system.service.CompanyWechatService;
import com.bxm.system.service.ISysEmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 员工Controller
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@RestController
@RequestMapping("/employee")
@Api(tags = "员工")
public class SysEmployeeController extends BaseController
{
    @Autowired
    private ISysEmployeeService sysEmployeeService;

    @Autowired
    private CompanyWechatService companyWechatService;

    /**
     * 查询员工列表
     */
//    @RequiresPermissions("system:employee:list")
    @GetMapping("/list")
    @ApiOperation(value = "查询员工列表", notes = "查询员工列表")
    public Result<IPage<SysEmployee>> list(SysEmployee sysEmployee, @RequestParam("pageNum") Integer pageNum,
                                           @RequestParam("pageSize") Integer pageSize, @RequestHeader("deptId") Long deptId)
    {
        return Result.ok(sysEmployeeService.employeeList(sysEmployee, deptId, pageNum, pageSize));
    }

//    /**
//     * 导出员工列表
//     */
//    @RequiresPermissions("system:employee:export")
//    @Log(title = "员工", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    @ApiOperation(value = "导出员工列表", notes = "导出员工列表")
//    public void export(HttpServletResponse response, SysEmployee sysEmployee)
//    {
//        List<SysEmployee> list = sysEmployeeService.selectSysEmployeeList(sysEmployee);
//        ExcelUtil<SysEmployee> util = new ExcelUtil<SysEmployee>(SysEmployee.class);
//        util.exportExcel(response, list, "员工数据");
//    }

    /**
     * 获取员工详细信息
     */
//    @RequiresPermissions("system:employee:query")
    @GetMapping(value = "/{employeeId}")
    @ApiOperation(value = "获取员工详细信息", notes = "获取员工详细信息")
    public AjaxResult getInfo(@PathVariable("employeeId") Long employeeId)
    {
        return success(sysEmployeeService.selectSysEmployeeByEmployeeId(employeeId));
    }

    /**
     * 新增员工
     */
//    @RequiresPermissions("system:employee:add")
    @Log(title = "员工", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增员工", notes = "新增员工")
    public AjaxResult add(@RequestBody SysEmployee sysEmployee)
    {
        return toAjax(sysEmployeeService.insertSysEmployee(sysEmployee));
    }

    /**
     * 修改员工
     */
//    @RequiresPermissions("system:employee:edit")
    @Log(title = "员工", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改员工", notes = "修改员工")
    public AjaxResult edit(@RequestBody SysEmployee sysEmployee)
    {
        return toAjax(sysEmployeeService.updateSysEmployee(sysEmployee));
    }

    /**
     * 删除员工
     */
//    @RequiresPermissions("system:employee:remove")
    @Log(title = "员工", businessType = BusinessType.DELETE)
	@DeleteMapping("/{employeeIds}")
    @ApiOperation(value = "删除员工", notes = "删除员工")
    public AjaxResult remove(@PathVariable Long[] employeeIds)
    {
        return toAjax(sysEmployeeService.deleteSysEmployeeByEmployeeIds(employeeIds));
    }

    @GetMapping("/getEmployeeByUserIdAndDeptId")
    public AjaxResult getEmployeeByUserIdAndDeptId(@RequestParam("userId") Long userId,
                                                   @RequestParam("deptId") Long deptId) {
        return success(sysEmployeeService.getEmployeeByUserIdAndDeptId(userId, deptId));
    }

    @GetMapping("/getEmployeeListByDeptId")
    public Result<List<SysEmployee>> getEmployeeListByDeptId(@RequestParam(value = "deptId", required = false) Long deptId) {
        return Result.ok(sysEmployeeService.getEmployeeListByDeptId(deptId));
    }

    @GetMapping("/getEmployeeListByHeaderDeptId")
    public Result<List<SysEmployee>> getEmployeeListByHeaderDeptId(@RequestHeader("deptId") Long deptId) {
        return Result.ok(sysEmployeeService.getEmployeeListByHeaderDeptId(deptId));
    }

    @PostMapping("/getBatchEmployeeByDeptIds")
    public Result<List<SysEmployee>> getBatchEmployeeByDeptIds(@RequestBody List<Long> deptIds) {
        return Result.ok(sysEmployeeService.getBatchEmployeeByDeptIds(deptIds));
    }

    @PostMapping("/getBatchEmployeeByIds")
    public Result<List<SysEmployee>> getBatchEmployeeByIds(@RequestBody List<Long> employeeIds) {
        return Result.ok(sysEmployeeService.getBatchEmployeeByIds(employeeIds));
    }

    @PostMapping("/getBindWechatEmployeesByDeptId")
    @ApiOperation("获取绑定企业微信的员工")
    public Result<List<SysEmployee>> getBindWechatEmployeesByDeptId(@RequestParam("deltId") @ApiParam("小组id") Long deptId) {
        return Result.ok(sysEmployeeService.getBindWechatEmployeesByDeptId(deptId));
    }

    @PostMapping("/sendMessage")
    @ApiOperation("发送消息")
    @Log(title = "发送企微通知", businessType = BusinessType.COMPANY_WECHAT_SEND_MESSAGE)
    public Result sendMessage(@RequestBody SendMessageVO vo) {
        companyWechatService.sendMessage(vo);
        return Result.ok();
    }

    @PostMapping("/urge")
    @ApiOperation("催办会计")
    @Log(title = "催办会计", businessType = BusinessType.COMPANY_WECHAT_SEND_MESSAGE)
    public Result urge(@RequestBody SendMessageVO vo) {
        companyWechatService.urge(vo);
        return Result.ok();
    }

    @PostMapping("/urgeV2")
    @ApiOperation("催办会计")
    public Result urgeV2(@RequestBody SendMessageVO vo) {
        companyWechatService.urgeV2(vo);
        return Result.ok();
    }

    @PostMapping("/sendTextMessage")
    @ApiOperation("内部接口")
    public Result sendTextMessage(@RequestBody SendMessageVO vo) {
        companyWechatService.sendTextMessage(vo);
        return Result.ok();
    }

    @PostMapping("/uploadCompanyWechatFile")
    @ApiOperation("上传企业微信文件")
    public Result<WechatFileResultDTO> uploadCompanyWechatFile(MultipartFile file,
                                                               @RequestParam("userId") @ApiParam("发送对象userId") Long userId) {
        return Result.ok(companyWechatService.uploadCompanyWechatFile(file, userId));
    }

    @PostMapping("/getBatchByUserIds")
    @ApiOperation("批量查询员工信息")
    public Result<List<SysEmployee>> getBatchByUserIds(@RequestBody List<Long> userIds) {
        return Result.ok(sysEmployeeService.getBatchByUserIds(userIds));
    }
}
