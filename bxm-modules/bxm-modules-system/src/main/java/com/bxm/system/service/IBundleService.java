package com.bxm.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.common.core.web.domain.CommonOperateResultDTO;
import com.bxm.system.domain.Bundle;
import com.bxm.system.domain.dto.bundle.BundleDTO;
import com.bxm.system.domain.dto.bundle.BundleDetailDTO;
import com.bxm.system.domain.vo.bundle.BundleAddVO;
import com.bxm.system.domain.vo.bundle.BundleDeleteVO;
import com.bxm.system.domain.vo.bundle.BundleModifyVO;
import com.bxm.system.domain.vo.bundle.BundleUpdateMenuVO;

import java.util.List;

/**
 * 套餐Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IBundleService extends IService<Bundle>
{
    /**
     * 查询套餐
     * 
     * @param id 套餐主键
     * @return 套餐
     */
    public Bundle selectBundleById(Long id);

    /**
     * 查询套餐列表
     * 
     * @param bundle 套餐
     * @return 套餐集合
     */
    public List<Bundle> selectBundleList(Bundle bundle);

    /**
     * 新增套餐
     * 
     * @param bundle 套餐
     * @return 结果
     */
    public int insertBundle(Bundle bundle);

    /**
     * 修改套餐
     * 
     * @param bundle 套餐
     * @return 结果
     */
    public int updateBundle(Bundle bundle);

    /**
     * 批量删除套餐
     * 
     * @param ids 需要删除的套餐主键集合
     * @return 结果
     */
    public int deleteBundleByIds(Long[] ids);

    /**
     * 删除套餐信息
     * 
     * @param id 套餐主键
     * @return 结果
     */
    public int deleteBundleById(Long id);

    IPage<BundleDTO> bundleList(Integer pageNum, Integer pageSize, String bundleName);

    void createBundle(BundleAddVO vo, Long deptId);

    void modifyBundle(BundleModifyVO vo, Long deptId);

    CommonOperateResultDTO deleteBundle(BundleDeleteVO vo, Long deptId);

    void addBundleMenu(BundleUpdateMenuVO vo, Long deptId);

    void deleteBundleMenu(BundleUpdateMenuVO vo, Long deptId);

    List<BundleDTO> bundleSelectList();

    BundleDetailDTO detail(Long id);
}
