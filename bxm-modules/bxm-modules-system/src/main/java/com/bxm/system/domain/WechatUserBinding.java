package com.bxm.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 企微用户与系统用户关联对象 system_wechat_user_binding
 * 
 * <AUTHOR>
 * @date 2024-06-23
 */
@Data
@ApiModel("企微用户与系统用户关联对象")
@Accessors(chain = true)
@TableName("sys_wechat_user_binding")
public class WechatUserBinding extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 企业微信用户表ID */
    @Excel(name = "企业微信用户表ID")
    @TableField("wechat_user_id")
    @ApiModelProperty(value = "企业微信用户表ID")
    private Long wechatUserId;

    /** 系统用户ID */
    @Excel(name = "系统用户ID")
    @TableField("system_user_id")
    @ApiModelProperty(value = "系统用户ID")
    private Long systemUserId;

}
