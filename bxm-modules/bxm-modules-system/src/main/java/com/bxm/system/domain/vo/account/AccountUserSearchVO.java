package com.bxm.system.domain.vo.account;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountUserSearchVO extends BaseVO {

    @ApiModelProperty("员工名")
    private String nickName;

    @ApiModelProperty("手机号")
    private String phonenumber;

    @ApiModelProperty("员工账号")
    private String userName;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("用户状态，0-正常，1-停用")
    private String userStatus;

    @ApiModelProperty("是否绑定通行证，true-已绑定，false-未绑定")
    private Boolean hasBindPassCheck;

    @ApiModelProperty("组织id，如果选中了某个组织传选中的组织id，如果取消了选择传集团id")
    private Long deptId;
}
