package com.bxm.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 套餐组织关系对象 sys_bundle_dept
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Data
@ApiModel("套餐组织关系对象")
@Accessors(chain = true)
@TableName("sys_bundle_dept")
public class BundleDept extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 套餐ID */
    @Excel(name = "套餐ID")
    @TableField("bundle_id")
    @ApiModelProperty(value = "套餐ID")
    private Long bundleId;

    /** 组织ID */
    @Excel(name = "组织ID")
    @TableField("dept_id")
    @ApiModelProperty(value = "组织ID")
    private Long deptId;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

}
