package com.bxm.system.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bxm.common.core.enums.BusinessLogBusinessType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.bxm.system.api.domain.SysRole;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import com.bxm.system.domain.*;
import com.bxm.system.domain.dto.account.AccountDeptDetailDTO;
import com.bxm.system.domain.dto.account.AccountUserDTO;
import com.bxm.system.domain.dto.account.AccountUserDetailDTO;
import com.bxm.system.domain.vo.account.*;
import com.bxm.system.mapper.SysDeptMapper;
import com.bxm.system.mapper.SysEmployeeMapper;
import com.bxm.system.mapper.SysUserMapper;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class AccountService {

    private static final String DEFAULT_PASSWORD = "hello1234";

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private IBusinessLogService businessLogService;

    @Autowired
    private SysEmployeeMapper sysEmployeeMapper;

    @Autowired
    private ISysEmployeeService sysEmployeeService;

    @Autowired
    private ISysUserService sysUserService;

    public SysDept getCurrentTopDept(Long deptId) {
        SysDept sysDept = sysDeptMapper.selectById(deptId);
        if (Objects.isNull(sysDept) || !"0".equals(sysDept.getDelFlag())) {
            throw new ServiceException("当前公司不存在");
        }
        if (sysDept.getLevel() != 2 || Objects.isNull(sysDept.getParentId()) || Objects.equals(sysDept.getParentId(), 0L)) {
            throw new ServiceException("当前公司有误");
        }
        return sysDeptMapper.selectById(sysDept.getParentId());
    }

    public IPage<AccountUserDTO> accountUserList(AccountUserSearchVO vo, Long deptId) {
        IPage<AccountUserDTO> result = new Page<>(vo.getPageNum(), vo.getPageSize());
        Long userId = SecurityUtils.getUserId();
        UserDeptDTO userDeptDTO = sysDeptService.userDeptList(userId, deptId);
        if (!userDeptDTO.getIsAdmin() && ObjectUtils.isEmpty(userDeptDTO.getDeptIds())) {
            return result;
        }
        SysDept sysDept = sysDeptMapper.selectById(vo.getDeptId());
        if (Objects.isNull(sysDept) || !"0".equals(sysDept.getDelFlag())) {
            return result;
        }
        List<Long> deptIds = Lists.newArrayList();
        if (sysDept.getLevel() == 1) {
            deptIds = sysDeptService.getAllChildrenIdByTopDeptId(vo.getDeptId());
        } else {
            deptIds.add(vo.getDeptId());
        }
        List<AccountUserDTO> data = sysUserMapper.selectAccountUserList(result, vo, userDeptDTO, deptIds);
        if (!ObjectUtils.isEmpty(data)) {
            List<Long> userIds = data.stream().map(AccountUserDTO::getUserId).collect(Collectors.toList());
            Map<Long, List<SysRole>> userRoleMap = sysRoleService.getUserRolesByUserIds(userIds);
            Map<Long, SysEmployee> employeeMap = sysEmployeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                    .eq(SysEmployee::getDeptId, vo.getDeptId()).in(SysEmployee::getUserId, userIds))
                    .stream().collect(Collectors.toMap(SysEmployee::getUserId, Function.identity(), (v1, v2) -> v1));
            data.forEach(d -> {
                List<SysRole> sysRoles = userRoleMap.get(d.getUserId());
                if (!ObjectUtils.isEmpty(sysRoles)) {
                    sysRoles.sort(Comparator.comparing(SysRole::getRoleId));
                }
                d.setRoleNames(ObjectUtils.isEmpty(sysRoles) ? "" : sysRoles.stream().map(SysRole::getRoleName).collect(Collectors.joining("、")));
                if (sysDept.getLevel() == 1) {
                    d.setIsLeader(false);
                } else {
                    SysEmployee employee = employeeMap.get(d.getUserId());
                    d.setIsLeader(!Objects.isNull(employee) && employee.getIsLeader());
                }
            });
        }
        result.setRecords(data);
        return result;
    }

    @Transactional
    public void resetPassword(List<Long> userIds, Long deptId) {
        List<SysUser> users = sysUserMapper.selectBatchIds(userIds);
        if (ObjectUtils.isEmpty(users)) {
            return;
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        users.forEach(user -> {
            SysUser update = new SysUser();
            update.setUserId(user.getUserId());
            update.setPassword(SecurityUtils.encryptPassword(DEFAULT_PASSWORD));
            update.setUpdateBy(currentUser.getNickName());
            sysUserMapper.updateById(update);

            saveUserBusinessLog(user.getUserId(), currentUser, "重置密码", deptId, null, null);
        });
    }

    @Transactional
    public void addAccountDept(AccountDeptAddVO vo, Long deptId) {
        if (checkDeptNameExists(vo.getDeptName(), vo.getTopDeptId(), null)) {
            throw new ServiceException("部门名已存在");
        }
        SysDept parentDept = sysDeptMapper.selectById(vo.getParentId());
        if (Objects.isNull(parentDept) || !"0".equals(parentDept.getDelFlag())) {
            throw new ServiceException("上级组织不存在");
        }
        if (checkParentIdLevel(parentDept)) {
            throw new ServiceException("上级组织最多为二级组织");
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        SysDept sysDept = new SysDept();
        sysDept.setParentId(vo.getParentId());
        sysDept.setAncestors(parentDept.getAncestors() + "," + parentDept.getDeptId());
        sysDept.setDeptType(parentDept.getDeptType());
        sysDept.setDeptName(vo.getDeptName());
        sysDept.setOrderNum(0);
        sysDept.setStatus("0");
        sysDept.setCapacity(vo.getCapacity());
        sysDept.setLevel(4);
        sysDept.setHasDataScope(false);
        sysDept.setIsHeadquarters(false);
        sysDept.setIsFunctional(false);
        sysDept.setDelFlag("0");
        sysDept.setCreateBy(currentUser.getNickName());
        sysDeptMapper.insert(sysDept);
        if (parentDept.getLevel() == 4) {
            SysDept parentUpdate = new SysDept();
            parentUpdate.setDeptId(parentDept.getDeptId());
            parentUpdate.setLevel(3);
            sysDeptMapper.updateById(parentUpdate);
        }
        saveDeptBusinessLog(sysDept.getDeptId(), currentUser, "新建", deptId, null, null);
    }

    @Transactional
    public AccountDeptDetailDTO accountDeptDetail(Long deptId) {
        SysDept sysDept = getDeptByDeptIdThrows(deptId);
        return AccountDeptDetailDTO.builder()
                .deptId(sysDept.getDeptId())
                .parentId(sysDept.getParentId())
                .deptPath(StringUtils.stringToLongList(sysDept.getAncestors()))
                .deptName(sysDept.getDeptName())
                .capacity(sysDept.getCapacity())
                .build();
    }

    @Transactional
    public void modifyAccountDept(AccountDeptModifyVO vo, Long deptId) {
        SysDept sysDept = getDeptByDeptIdThrows(vo.getDeptId());
        if (sysDept.getLevel() < 2) {
            throw new ServiceException("只允许编辑3级和4级部门");
        }
        if (checkDeptNameExists(vo.getDeptName(), vo.getTopDeptId(), vo.getDeptId())) {
            throw new ServiceException("部门名已存在");
        }
        Long oldParentId = sysDept.getParentId();
        SysDept oldParentDept = sysDeptMapper.selectById(oldParentId);
        if (Objects.isNull(oldParentDept) || !"0".equals(oldParentDept.getDelFlag())) {
            throw new ServiceException("原上级组织不存在");
        }
        SysDept newParentDept = sysDeptMapper.selectById(vo.getParentId());
        if (Objects.isNull(newParentDept) || !"0".equals(newParentDept.getDelFlag())) {
            throw new ServiceException("上级组织不存在");
        }
        if (checkParentIdLevel(newParentDept)) {
            throw new ServiceException("上级组织最多为二级组织");
        }
        if (Objects.equals(vo.getParentId(), vo.getDeptId())) {
            throw new ServiceException("上级组织不能为当前组织");
        }
        if (StringUtils.stringToLongList(newParentDept.getAncestors()).contains(vo.getDeptId())) {
            throw new ServiceException("上级组织不能为当前组织的下级组织");
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        sysDeptService.update(new LambdaUpdateWrapper<SysDept>()
                .eq(SysDept::getDeptId, vo.getDeptId())
                .set(SysDept::getParentId, vo.getParentId())
                .set(SysDept::getAncestors, newParentDept.getAncestors() + "," + newParentDept.getDeptId())
                .set(SysDept::getDeptType, newParentDept.getDeptType())
                .set(SysDept::getDeptName, vo.getDeptName())
                .set(SysDept::getCapacity, vo.getCapacity())
                .set(SysDept::getUpdateBy, currentUser.getNickName())
        );
        if (!Objects.equals(oldParentId, vo.getParentId())) {
            if (newParentDept.getLevel() == 4) {
                SysDept parentUpdate = new SysDept();
                parentUpdate.setDeptId(newParentDept.getDeptId());
                parentUpdate.setLevel(3);
                sysDeptMapper.updateById(parentUpdate);
            }
            if (oldParentDept.getLevel() == 3 && !checkHasChildDept(oldParentDept.getDeptId())) {
                SysDept parentUpdate = new SysDept();
                parentUpdate.setDeptId(oldParentDept.getDeptId());
                parentUpdate.setLevel(4);
                sysDeptMapper.updateById(parentUpdate);
            }
            if (sysDept.getLevel() == 3) {
                updateChildDeptAncestors(sysDept.getDeptId(), oldParentId, vo.getParentId());
            }
        }
        Map<String, Object> operContent = buildModifyDeptOperContent(sysDept, vo, oldParentDept, newParentDept);
        saveDeptBusinessLog(sysDept.getDeptId(), currentUser, "编辑", deptId, operContent, null);
    }

    @Transactional
    public void deleteAccountDept(Long id, Long deptId) {
        SysDept sysDept = getDeptByDeptIdThrows(deptId);
        if (sysDept.getLevel() < 2) {
            throw new ServiceException("只允许删除3级和4级部门");
        }
        checkDeptCanDelete(id);
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        SysDept update = new SysDept();
        update.setDeptId(id);
        update.setDelFlag("2");
        update.setUpdateBy(currentUser.getNickName());
        sysDeptMapper.updateById(update);

        saveDeptBusinessLog(sysDept.getDeptId(), currentUser, "删除", deptId, null, null);
    }

    @Transactional
    public void addAccountUser(AccountUserAddVO vo, Long deptId) {
        SysDept sysDept = getDeptByDeptIdThrows(deptId);
        if (sysDept.getLevel() != 2 || Objects.isNull(sysDept.getParentId()) || Objects.equals(0L, sysDept.getParentId())) {
            throw new ServiceException("当前公司有误");
        }
        vo.setUserId(SecurityUtils.getUserId());
        vo.setDeptId(deptId);
        createAccountUser(vo, sysDept.getParentId());
    }

    private void createAccountUser(AccountUserAddVO vo, Long topDeptId) {
        checkUserNameExists(vo.getUserName(), topDeptId, null);
        checkNickNameExists(vo.getNickName(), topDeptId, null);
        checkPhonenumberExists(vo.getPhonenumber(), topDeptId, null);
        SysUser currentUser = sysUserMapper.selectById(vo.getUserId());
        SysUser sysUser = new SysUser();
        sysUser.setUserName(vo.getUserName());
        sysUser.setNickName(vo.getNickName());
        sysUser.setPhonenumber(vo.getPhonenumber());
        sysUser.setPassword(SecurityUtils.encryptPassword(DEFAULT_PASSWORD));
        sysUser.setStatus("0");
        sysUser.setDelFlag("0");
        sysUser.setCreateBy(currentUser.getNickName());
        sysUserMapper.insert(sysUser);

        if (!ObjectUtils.isEmpty(vo.getRoleIds())) {
            sysRoleService.saveUserRoles(sysUser.getUserId(), vo.getRoleIds());
        }
        if (!ObjectUtils.isEmpty(vo.getDeptIds())) {
            sysEmployeeService.saveUserDept(sysUser, vo.getDeptIds(), vo.getIsLeader(), vo.getNickName(), vo.getPhonenumber());
        }
        saveUserBusinessLog(sysUser.getUserId(), currentUser, "新建", vo.getDeptId(), null, null);
    }

    @Transactional
    public void remoteAddAccountUser(AccountUserAddVO vo) {
        SysDept sysDept = getDeptByDeptIdThrows(vo.getDeptId());
        if (sysDept.getLevel() != 2 || Objects.isNull(sysDept.getParentId()) || Objects.equals(0L, sysDept.getParentId())) {
            throw new ServiceException("当前公司有误");
        }
        createAccountUser(vo, sysDept.getParentId());
    }

    public AccountUserDetailDTO accountUserDetail(Long userId) {
        SysUser sysUser = getUserByUserIdThrows(userId);
        Map<Long, List<SysRole>> userRoleMap = sysRoleService.getUserRolesByUserIds(Collections.singletonList(userId));
        List<SysRole> sysRoles = userRoleMap.get(userId);
        List<SysEmployee> employees = sysEmployeeService.list(new LambdaQueryWrapper<SysEmployee>()
                .eq(SysEmployee::getUserId, userId));
        List<Long> deptIds = employees.stream().map(SysEmployee::getDeptId).collect(Collectors.toList());
        List<List<Long>> deptPathList = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(deptIds)) {
            List<SysDept> depts = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                    .eq(SysDept::getDelFlag, "0")
                    .in(SysDept::getDeptId, deptIds));
            for (SysDept dept : depts) {
                List<Long> deptIdPath = StringUtils.stringToLongList(dept.getAncestors());
                deptIdPath.add(dept.getDeptId());
                deptPathList.add(deptIdPath);
            }
        }
        return AccountUserDetailDTO.builder()
                .userId(userId)
                .userName(sysUser.getUserName())
                .nickName(sysUser.getNickName())
                .phonenumber(sysUser.getPhonenumber())
                .roleIds(ObjectUtils.isEmpty(sysRoles) ? Lists.newArrayList() :
                        sysRoles.stream().map(SysRole::getRoleId).collect(Collectors.toList()))
                .deptIds(deptIds)
                .deptPathList(deptPathList)
                .isLeader(!ObjectUtils.isEmpty(employees) && employees.get(0).getIsLeader())
                .build();
    }

    @Transactional
    public void modifyAccountUser(AccountUserModifyVO vo, Long deptId) {
        SysUser sysUser = getUserByUserIdThrows(vo.getUserId());
        SysDept sysDept = getDeptByDeptIdThrows(deptId);
        if (sysDept.getLevel() != 2 || Objects.isNull(sysDept.getParentId()) || Objects.equals(0L, sysDept.getParentId())) {
            throw new ServiceException("当前公司有误");
        }
        checkUserNameExists(vo.getUserName(), sysDept.getParentId(), vo.getUserId());
        checkNickNameExists(vo.getNickName(), sysDept.getParentId(), vo.getUserId());
        checkPhonenumberExists(vo.getPhonenumber(), sysDept.getParentId(), vo.getUserId());
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        sysUserService.update(new LambdaUpdateWrapper<SysUser>()
                .eq(SysUser::getUserId, vo.getUserId())
                .set(SysUser::getUserName, vo.getUserName())
                .set(SysUser::getNickName, vo.getNickName())
                .set(SysUser::getPhonenumber, vo.getPhonenumber()));
        Map<Long, List<SysRole>> userRoleMap = sysRoleService.getUserRolesByUserIds(Collections.singletonList(vo.getUserId()));
        List<SysRole> oldRoles = userRoleMap.getOrDefault(vo.getUserId(), Lists.newArrayList());
        List<SysRole> newRoles = ObjectUtils.isEmpty(vo.getRoleIds()) ? Lists.newArrayList() :
                sysRoleService.list(new LambdaQueryWrapper<SysRole>()
                .in(SysRole::getRoleId, vo.getRoleIds()).eq(SysRole::getDelFlag, "0"));
        List<SysDept> oldDepts = sysEmployeeService.selectDeptsByUserId(vo.getUserId());
        List<SysDept> newDepts = ObjectUtils.isEmpty(vo.getDeptIds()) ? Lists.newArrayList() :
                sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDelFlag, "0")
                .in(SysDept::getDeptId, vo.getDeptIds()));
        sysRoleService.deleteRoleUserByUserId(vo.getUserId());
        if (!ObjectUtils.isEmpty(vo.getRoleIds())) {
            sysRoleService.saveUserRoles(sysUser.getUserId(), vo.getRoleIds());
        }
        sysEmployeeService.removeByUserId(vo.getUserId());
        if (!ObjectUtils.isEmpty(vo.getDeptIds())) {
            sysEmployeeService.saveUserDept(sysUser, vo.getDeptIds(), vo.getIsLeader(), vo.getNickName(), vo.getPhonenumber());
        }
        Map<String, Object> operContent = buildModifyUserOperContent(sysUser, vo, oldRoles, newRoles, oldDepts, newDepts);
        saveUserBusinessLog(sysUser.getUserId(), currentUser, "编辑", deptId, operContent, null);
    }

    @Transactional
    public void batchAddUserRole(AccountUserUpdateRoleVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getUserIds())) {
            throw new ServiceException("请选择要操作的账号");
        }
        if (ObjectUtils.isEmpty(vo.getRoleIds())) {
            throw new ServiceException("请选择要添加的角色");
        }
        List<SysRole> roles = sysRoleService.list(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getDelFlag, "0").in(SysRole::getRoleId, vo.getRoleIds()));
        if (ObjectUtils.isEmpty(roles)) {
            throw new ServiceException("角色不存在");
        }
        List<SysUser> users = sysUserService.list(new LambdaQueryWrapper<SysUser>().eq(SysUser::getDelFlag, "0")
                .in(SysUser::getUserId, vo.getUserIds()));
        if (ObjectUtils.isEmpty(users)) {
            return;
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        Map<Long, List<SysRole>> roleUserMap = sysRoleService.getUserRolesByUserIds(users.stream().map(SysUser::getUserId).collect(Collectors.toList()));
        users.forEach(user -> createRoleUser(user, roleUserMap, roles, currentUser, deptId));
    }

    @Transactional
    public void batchRemoveUserRole(AccountUserUpdateRoleVO vo, Long deptId) {
        if (ObjectUtils.isEmpty(vo.getUserIds())) {
            throw new ServiceException("请选择要操作的账号");
        }
        if (ObjectUtils.isEmpty(vo.getRoleIds())) {
            throw new ServiceException("请选择要删除的角色");
        }
        List<SysRole> roles = sysRoleService.list(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getDelFlag, "0").in(SysRole::getRoleId, vo.getRoleIds()));
        if (ObjectUtils.isEmpty(roles)) {
            throw new ServiceException("角色不存在");
        }
        List<SysUser> users = sysUserService.list(new LambdaQueryWrapper<SysUser>().eq(SysUser::getDelFlag, "0")
                .in(SysUser::getUserId, vo.getUserIds()));
        if (ObjectUtils.isEmpty(users)) {
            return;
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        Map<Long, List<SysRole>> roleUserMap = sysRoleService.getUserRolesByUserIds(users.stream().map(SysUser::getUserId).collect(Collectors.toList()));
        users.forEach(user -> removeRoleUser(user, roleUserMap, roles, currentUser, deptId));
    }

    @Transactional
    public void openStopAccountUser(AccountUserOpenStopVO vo, Long deptId) {
        List<SysUser> users = sysUserService.list(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getDelFlag, "0").in(SysUser::getUserId, vo.getUserIds())
                .eq(SysUser::getStatus, vo.getDealResult()));
        if (ObjectUtils.isEmpty(users)) {
            return;
        }
        SysUser currentUser = SecurityUtils.getLoginUser().getSysUser();
        String dealResultStr = vo.getDealResult() == 0 ? "停用" : "启用";
        users.forEach(user -> {
            SysUser update = new SysUser();
            update.setUserId(user.getUserId());
            update.setStatus(vo.getDealResult() == 0 ? "1" : "0");
            update.setUpdateBy(currentUser.getNickName());
            sysUserService.updateById(update);

            saveUserBusinessLog(user.getUserId(), currentUser, dealResultStr, deptId, null, null);
        });
    }

    private void saveUserBusinessLog(Long businessId, SysUser currentUser, String operType, Long deptId, Map<String, Object> operContent, String operRemark) {
        BusinessLog businessLog = new BusinessLog().setBusinessId(businessId)
                .setBusinessType(BusinessLogBusinessType.ACCOUNT.getCode())
                .setOperName(currentUser.getNickName())
                .setOperType(operType)
                .setOperUserId(currentUser.getUserId())
                .setDeptName("")
                .setDeptId(deptId)
                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                .setOperRemark(operRemark);

        businessLogService.insertBusinessLog(businessLog);
    }

    private void saveDeptBusinessLog(Long businessId, SysUser currentUser, String operType, Long deptId, Map<String, Object> operContent, String operRemark) {
        BusinessLog businessLog = new BusinessLog().setBusinessId(businessId)
                .setBusinessType(BusinessLogBusinessType.DEPT.getCode())
                .setOperName(currentUser.getNickName())
                .setOperType(operType)
                .setOperUserId(currentUser.getUserId())
                .setDeptName("")
                .setDeptId(deptId)
                .setOperContent(ObjectUtils.isEmpty(operContent) ? "" : JSONObject.toJSONString(operContent))
                .setOperRemark(operRemark);

        businessLogService.insertBusinessLog(businessLog);
    }

    private boolean checkDeptNameExists(String deptName, Long topDeptId, Long id) {
        return sysDeptMapper.selectCount(new QueryWrapper<SysDept>()
                .eq("dept_name", deptName)
                .eq("del_flag", "0")
                .and(wrapper -> wrapper.eq("dept_id", topDeptId)
                        .or().like("concat(',', ancestors, ',')", "," + topDeptId + ","))
                .ne(!Objects.isNull(id), "dept_id", id)) > 0;
    }

    private boolean checkParentIdLevel(SysDept sysDept) {
        return sysDept.getLevel() < 2;
    }

    public SysDept getDeptByDeptIdThrows(Long deptId) {
        SysDept sysDept = sysDeptMapper.selectById(deptId);
        if (Objects.isNull(sysDept) || !"0".equals(sysDept.getDelFlag())) {
            throw new ServiceException("组织不存在");
        }
        return sysDept;
    }

    private SysUser getUserByUserIdThrows(Long userId) {
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (Objects.isNull(sysUser) || !"0".equals(sysUser.getDelFlag())) {
            throw new ServiceException("用户不存在");
        }
        return sysUser;
    }

    private boolean checkHasChildDept(Long deptId) {
        return sysDeptMapper.selectCount(new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDelFlag, "0")
                .eq(SysDept::getParentId, deptId)) > 0;
    }

    private void updateChildDeptAncestors(Long deptId, Long oldParentId, Long newParentId) {
        List<SysDept> children = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getParentId, deptId)
                .eq(SysDept::getDelFlag, "0"));
        if (!ObjectUtils.isEmpty(children)) {
            sysDeptMapper.updateAncestors(children.stream().map(SysDept::getDeptId).collect(Collectors.toList()),
                    "," + oldParentId + ",", "," + newParentId + ",");
        }
    }

    private Map<String, Object> buildModifyDeptOperContent(SysDept sysDept, AccountDeptModifyVO vo, SysDept oldParentDept, SysDept newParentDept) {
        Map<String, Object> operContent = new LinkedHashMap<>();
        if (!Objects.equals(oldParentDept.getDeptId(), newParentDept.getDeptId())) {
            operContent.put("上级组织", newParentDept.getDeptId() + newParentDept.getDeptName());
        }
        if (!Objects.equals(sysDept.getDeptName(), vo.getDeptName())) {
            operContent.put("部门名称", vo.getDeptName());
        }
        if (!Objects.equals(sysDept.getCapacity(), vo.getCapacity())) {
            if (Objects.isNull(vo.getCapacity())) {
                operContent.put("容量", "清空");
            } else {
                operContent.put("容量", vo.getCapacity());
            }
        }
        return operContent;
    }

    private void checkDeptCanDelete(Long deptId) {
        if (checkHasChildDept(deptId)) {
            throw new ServiceException("存在下属组织时，不允许删除");
        }
        if (checkHasEmployee(deptId)) {
            throw new ServiceException("组织下存在用户时，不允许删除");
        }
        if (sysDeptMapper.countCustomerServicePeriodAdvisorDept(deptId) > 0) {
            throw new ServiceException("存在账期顾问，不允许删除");
        }
        if (sysDeptMapper.countCustomerServicePeriodAccountingDept(deptId) > 0) {
            throw new ServiceException("存在账期会计，不允许删除");
        }
        if (sysDeptMapper.countWorkOrderInitiateDept(deptId) > 0) {
            throw new ServiceException("存在工单发起小组为当前小组，不允许删除");
        }
        if (sysDeptMapper.countWorkOrderUndertakeDept(deptId) > 0) {
            throw new ServiceException("存在工单承接小组为当前小组，不允许删除");
        }
    }

    private boolean checkHasEmployee(Long deptId) {
        return sysEmployeeService.count(new LambdaQueryWrapper<SysEmployee>()
                .eq(SysEmployee::getDeptId, deptId)) > 0;
    }

    public void checkUserNameExists(String userName, Long topDeptId, Long userId) {
        SysUser sysUser = sysUserMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserName, userName).eq(SysUser::getDelFlag, "0")
                .ne(!Objects.isNull(userId), SysUser::getUserId, userId)
                .last("limit 1"));
        if (Objects.isNull(sysUser)) {
            return;
        }
        SysEmployee employee = sysEmployeeMapper.selectOne(new LambdaQueryWrapper<SysEmployee>()
                .eq(SysEmployee::getUserId, sysUser.getUserId()).last("limit 1"));
        if (Objects.isNull(employee)) {
            throw new ServiceException("账号已被占用，请更换账号");
        }
        SysDept dept = sysDeptMapper.selectById(employee.getDeptId());
        Long topDept = Long.parseLong(dept.getAncestors().split(",")[1]);
        if (Objects.equals(topDept, topDeptId)) {
            throw new ServiceException("该账号已存在，请更换或启用");
        } else {
            throw new ServiceException("账号已被占用，请更换账号");
        }
    }

    public void checkNickNameExists(String nickName, Long topDeptId, Long userId) {
        List<SysUser> userList = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getNickName, nickName).eq(SysUser::getDelFlag, "0")
                .ne(!Objects.isNull(userId), SysUser::getUserId, userId));
        if (ObjectUtils.isEmpty(userList)) {
            return;
        }
        List<SysEmployee> employees = sysEmployeeMapper.selectList(new LambdaQueryWrapper<SysEmployee>()
                .in(SysEmployee::getUserId, userList.stream().map(SysUser::getUserId).collect(Collectors.toList())));
        if (ObjectUtils.isEmpty(employees)) {
            throw new ServiceException("员工名已存在");
        }
        Map<Long, SysDept> deptMap = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .in(SysDept::getDeptId, employees.stream().map(SysEmployee::getDeptId).collect(Collectors.toList()))
                .eq(SysDept::getDelFlag, "0")).stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        for (SysEmployee employee : employees) {
            SysDept dept = deptMap.get(employee.getDeptId());
            Long topDept = Long.parseLong(dept.getAncestors().split(",")[1]);
            if (Objects.equals(topDept, topDeptId)) {
                throw new ServiceException("员工名已存在");
            }
        }
    }

    public void checkPhonenumberExists(String phonenumber, Long topDeptId, Long userId) {
        SysUser sysUser = sysUserMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getPhonenumber, phonenumber).eq(SysUser::getDelFlag, "0")
                .ne(!Objects.isNull(userId), SysUser::getUserId, userId)
                .last("limit 1"));
        if (Objects.isNull(sysUser)) {
            return;
        }
        SysEmployee employee = sysEmployeeMapper.selectOne(new LambdaQueryWrapper<SysEmployee>()
                .eq(SysEmployee::getUserId, sysUser.getUserId()).last("limit 1"));
        if (Objects.isNull(employee)) {
            throw new ServiceException("手机号已被占用，请更换账号");
        }
        SysDept dept = sysDeptMapper.selectById(employee.getDeptId());
        Long topDept = Long.parseLong(dept.getAncestors().split(",")[1]);
        if (Objects.equals(topDept, topDeptId)) {
            throw new ServiceException("该手机号已存在，请更换或启用");
        } else {
            throw new ServiceException("手机号已被占用，请更换账号");
        }
    }

    private Map<String, Object> buildModifyUserOperContent(SysUser sysUser, AccountUserModifyVO vo, List<SysRole> oldRoles, List<SysRole> newRoles, List<SysDept> oldDepts, List<SysDept> newDepts) {
        Map<String, Object> operContent = new LinkedHashMap<>();
        if (!Objects.equals(sysUser.getUserName(), vo.getUserName())) {
            operContent.put("账号", vo.getUserName());
        }
        if (!Objects.equals(sysUser.getNickName(), vo.getNickName())) {
            operContent.put("员工名", vo.getNickName());
        }
        if (!Objects.equals(sysUser.getPhonenumber(), vo.getPhonenumber())) {
            operContent.put("手机号", vo.getPhonenumber());
        }
        Set<Long> oldRoleIds = oldRoles.stream().map(SysRole::getRoleId).collect(Collectors.toSet());
        Set<Long> newRoleIds = newRoles.stream().map(SysRole::getRoleId).collect(Collectors.toSet());

        Set<Long> addedRoleIds = new HashSet<>(newRoleIds);
        addedRoleIds.removeAll(oldRoleIds);

        Set<Long> removedRoleIds = new HashSet<>(oldRoleIds);
        removedRoleIds.removeAll(newRoleIds);

        if (!addedRoleIds.isEmpty()) {
            String addRoles = newRoles.stream()
                    .filter(role -> addedRoleIds.contains(role.getRoleId()))
                    .map(role -> role.getRoleId() + role.getRoleName())
                    .collect(Collectors.joining("，"));
            operContent.put("增加角色", addRoles);
        }

        // 构建删除权限信息
        if (!removedRoleIds.isEmpty()) {
            String removedRoles = oldRoles.stream()
                    .filter(role -> removedRoleIds.contains(role.getRoleId()))
                    .map(role -> role.getRoleId() + role.getRoleName())
                    .collect(Collectors.joining("，"));
            operContent.put("删除角色", removedRoles);
        }
        Set<Long> oldDeptIds = oldDepts.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
        Set<Long> newDeptIds = newDepts.stream().map(SysDept::getDeptId).collect(Collectors.toSet());

        Set<Long> addedDeptIds = new HashSet<>(newDeptIds);
        addedDeptIds.removeAll(oldDeptIds);

        Set<Long> removedDeptIds = new HashSet<>(oldDeptIds);
        removedDeptIds.removeAll(newDeptIds);

        if (!addedDeptIds.isEmpty()) {
            String addDepts = newDepts.stream()
                    .filter(dept -> addedDeptIds.contains(dept.getDeptId()))
                    .map(dept -> dept.getDeptId() + dept.getDeptName())
                    .collect(Collectors.joining("，"));
            operContent.put("增加部门", addDepts);
        }

        // 构建删除权限信息
        if (!removedDeptIds.isEmpty()) {
            String removedDepts = oldDepts.stream()
                    .filter(dept -> removedDeptIds.contains(dept.getDeptId()))
                    .map(dept -> dept.getDeptId() + dept.getDeptName())
                    .collect(Collectors.joining("，"));
            operContent.put("删除部门", removedDepts);
        }
        operContent.put("是否主管", vo.getIsLeader() ? "是" : "否");
        return operContent;
    }

    private void createRoleUser(SysUser user, Map<Long, List<SysRole>> roleUserMap, List<SysRole> roles, SysUser currentUser, Long deptId) {
        List<Long> oldRoleIds = roleUserMap.getOrDefault(user.getUserId(), Lists.newArrayList())
                .stream().map(SysRole::getRoleId).collect(Collectors.toList());
        List<Long> newRoleIds = roles.stream().map(SysRole::getRoleId).collect(Collectors.toList());
        List<Long> addRoleIds = newRoleIds.stream().filter(roleId -> !oldRoleIds.contains(roleId)).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(addRoleIds)) {
            return;
        }
        sysRoleService.saveUserRoles(user.getUserId(), addRoleIds);
        Map<String, Object> operContent = new LinkedHashMap<>();
        String addRoles = roles.stream()
                .filter(role -> addRoleIds.contains(role.getRoleId()))
                .map(role -> role.getRoleId() + role.getRoleName())
                .collect(Collectors.joining("，"));
        operContent.put("增加角色", addRoles);
        saveUserBusinessLog(user.getUserId(), currentUser, "批量增加角色", deptId, operContent, null);
    }

    private void removeRoleUser(SysUser user, Map<Long, List<SysRole>> roleUserMap, List<SysRole> roles, SysUser currentUser, Long deptId) {
        List<Long> oldRoleIds = roleUserMap.getOrDefault(user.getUserId(), Lists.newArrayList())
                .stream().map(SysRole::getRoleId).collect(Collectors.toList());
        List<Long> newRoleIds = roles.stream().map(SysRole::getRoleId).collect(Collectors.toList());
        List<Long> removeRoleIds = newRoleIds.stream().filter(oldRoleIds::contains).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(removeRoleIds)) {
            return;
        }
        sysRoleService.deleteUserRole(user.getUserId(), removeRoleIds);
        Map<String, Object> operContent = new LinkedHashMap<>();
        String removeRoles = roles.stream()
                .filter(role -> removeRoleIds.contains(role.getRoleId()))
                .map(role -> role.getRoleId() + role.getRoleName())
                .collect(Collectors.joining("，"));
        operContent.put("删除角色", removeRoles);
        saveUserBusinessLog(user.getUserId(), currentUser, "批量删除角色", deptId, operContent, null);
    }
}
