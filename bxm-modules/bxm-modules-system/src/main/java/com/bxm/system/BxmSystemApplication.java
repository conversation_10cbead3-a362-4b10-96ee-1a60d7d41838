package com.bxm.system;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.bxm.common.security.annotation.EnableCustomConfig;
import com.bxm.common.security.annotation.EnableJsFeignClients;
import com.bxm.common.swagger.annotation.EnableCustomSwagger2;

/**
 * 系统模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableJsFeignClients
@SpringBootApplication
public class BxmSystemApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(BxmSystemApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  系统模块启动成功   ");
    }
}
