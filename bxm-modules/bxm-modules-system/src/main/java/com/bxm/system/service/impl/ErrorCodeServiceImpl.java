package com.bxm.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.redis.service.RedisService;
import com.bxm.system.domain.ErrorCode;
import com.bxm.system.mapper.ErrorCodeMapper;
import com.bxm.system.service.IErrorCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 错误信息配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Service
public class ErrorCodeServiceImpl extends ServiceImpl<ErrorCodeMapper, ErrorCode> implements IErrorCodeService
{
    @Autowired
    private ErrorCodeMapper errorCodeMapper;

    @Autowired
    private RedisService redisService;

    /**
     * 查询错误信息配置
     * 
     * @param id 错误信息配置主键
     * @return 错误信息配置
     */
    @Override
    public ErrorCode selectErrorCodeById(Integer id)
    {
        return errorCodeMapper.selectErrorCodeById(id);
    }

    /**
     * 查询错误信息配置列表
     * 
     * @param errorCode 错误信息配置
     * @return 错误信息配置
     */
    @Override
    public List<ErrorCode> selectErrorCodeList(ErrorCode errorCode)
    {
        return errorCodeMapper.selectErrorCodeList(errorCode);
    }

    /**
     * 新增错误信息配置
     * 
     * @param errorCode 错误信息配置
     * @return 结果
     */
    @Override
    public int insertErrorCode(ErrorCode errorCode)
    {
        errorCode.setCreateTime(DateUtils.getNowDate());
        return errorCodeMapper.insertErrorCode(errorCode);
    }

    /**
     * 修改错误信息配置
     * 
     * @param errorCode 错误信息配置
     * @return 结果
     */
    @Override
    public int updateErrorCode(ErrorCode errorCode)
    {
        errorCode.setUpdateTime(DateUtils.getNowDate());
        return errorCodeMapper.updateErrorCode(errorCode);
    }

    /**
     * 批量删除错误信息配置
     * 
     * @param ids 需要删除的错误信息配置主键
     * @return 结果
     */
    @Override
    public int deleteErrorCodeByIds(Integer[] ids)
    {
        return errorCodeMapper.deleteErrorCodeByIds(ids);
    }

    /**
     * 删除错误信息配置信息
     * 
     * @param id 错误信息配置主键
     * @return 结果
     */
    @Override
    public int deleteErrorCodeById(Integer id)
    {
        return errorCodeMapper.deleteErrorCodeById(id);
    }

    @Override
    public String getErrorMsgByErrorCode(String errorCode) {
        Object value = redisService.getCacheObject(CacheConstants.ERROR_CODE_KEY + errorCode);
        if (!Objects.isNull(value)) {
            return value.toString();
        }
        ErrorCode one = getOne(new LambdaQueryWrapper<ErrorCode>().eq(ErrorCode::getErrorCode, errorCode));
        if (!Objects.isNull(one)) {
            redisService.setCacheObject(CacheConstants.ERROR_CODE_KEY + errorCode, one.getErrorMsg());
            return one.getErrorMsg();
        }
        return "";
    }
}
