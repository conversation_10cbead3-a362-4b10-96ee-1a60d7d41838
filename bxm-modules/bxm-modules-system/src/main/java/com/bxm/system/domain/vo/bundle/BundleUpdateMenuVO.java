package com.bxm.system.domain.vo.bundle;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BundleUpdateMenuVO {

    @ApiModelProperty("套餐id列表")
    private List<Long> ids;

    @ApiModelProperty("选中的菜单id列表")
    private List<Long> menuIds;
}
