package com.bxm.system.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.system.domain.UserCompanyRole;

/**
 * 公司用户关联Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface IUserCompanyRoleService extends IService<UserCompanyRole>
{
    /**
     * 查询公司用户关联
     * 
     * @param userCompanyId 公司用户关联主键
     * @return 公司用户关联
     */
    public UserCompanyRole selectUserCompanyRoleByUserCompanyId(Long userCompanyId);

    /**
     * 查询公司用户关联列表
     * 
     * @param userCompanyRole 公司用户关联
     * @return 公司用户关联集合
     */
    public List<UserCompanyRole> selectUserCompanyRoleList(UserCompanyRole userCompanyRole);

    /**
     * 新增公司用户关联
     * 
     * @param userCompanyRole 公司用户关联
     * @return 结果
     */
    public int insertUserCompanyRole(UserCompanyRole userCompanyRole);

    /**
     * 修改公司用户关联
     * 
     * @param userCompanyRole 公司用户关联
     * @return 结果
     */
    public int updateUserCompanyRole(UserCompanyRole userCompanyRole);

    /**
     * 批量删除公司用户关联
     * 
     * @param userCompanyIds 需要删除的公司用户关联主键集合
     * @return 结果
     */
    public int deleteUserCompanyRoleByUserCompanyIds(Long[] userCompanyIds);

    /**
     * 删除公司用户关联信息
     * 
     * @param userCompanyId 公司用户关联主键
     * @return 结果
     */
    public int deleteUserCompanyRoleByUserCompanyId(Long userCompanyId);
}
