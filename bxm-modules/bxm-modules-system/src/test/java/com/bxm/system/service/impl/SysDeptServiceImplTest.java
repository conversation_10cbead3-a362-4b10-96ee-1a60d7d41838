package com.bxm.system.service.impl;

import com.bxm.system.api.domain.SysUser;
import com.bxm.system.mapper.SysUserMapper;
import com.bxm.system.service.ISysDeptService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SysDeptServiceImplTest {

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Test
    void getDeptOrderByName() {
        System.out.println(sysDeptService.getDeptOrderByName());
    }

    @Test
    void getAllUserDeptNames() {
        SysUser user = sysUserMapper.selectById(1);
        System.out.println(sysDeptService.getAllUserDeptNames(user));
    }

    @Test
    void userDeptList() {
        System.out.println(sysDeptService.userDeptList(74L, 60L));
    }
}