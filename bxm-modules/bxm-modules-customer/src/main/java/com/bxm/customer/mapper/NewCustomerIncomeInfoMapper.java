package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerIncomeInfo;

/**
 * 新户流转收入信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerIncomeInfoMapper extends BaseMapper<NewCustomerIncomeInfo>
{
    /**
     * 查询新户流转收入信息
     * 
     * @param id 新户流转收入信息主键
     * @return 新户流转收入信息
     */
    public NewCustomerIncomeInfo selectNewCustomerIncomeInfoById(Long id);

    /**
     * 查询新户流转收入信息列表
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 新户流转收入信息集合
     */
    public List<NewCustomerIncomeInfo> selectNewCustomerIncomeInfoList(NewCustomerIncomeInfo newCustomerIncomeInfo);

    /**
     * 新增新户流转收入信息
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 结果
     */
    public int insertNewCustomerIncomeInfo(NewCustomerIncomeInfo newCustomerIncomeInfo);

    /**
     * 修改新户流转收入信息
     * 
     * @param newCustomerIncomeInfo 新户流转收入信息
     * @return 结果
     */
    public int updateNewCustomerIncomeInfo(NewCustomerIncomeInfo newCustomerIncomeInfo);

    /**
     * 删除新户流转收入信息
     * 
     * @param id 新户流转收入信息主键
     * @return 结果
     */
    public int deleteNewCustomerIncomeInfoById(Long id);

    /**
     * 批量删除新户流转收入信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerIncomeInfoByIds(Long[] ids);
}
