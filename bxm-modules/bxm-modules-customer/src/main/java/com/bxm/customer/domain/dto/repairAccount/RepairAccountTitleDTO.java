package com.bxm.customer.domain.dto.repairAccount;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/22 19:34
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairAccountTitleDTO {
    @ApiModelProperty("类型，1-补账，2材料交接单")
    private Integer type;

    @ApiModelProperty("对应ID")
    private Long id;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("批次序号")
    private Integer batchNum;

    @ApiModelProperty("标题全称")
    private String titleFull;
}
