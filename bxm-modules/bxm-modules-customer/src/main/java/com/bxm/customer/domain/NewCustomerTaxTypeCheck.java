package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 新户流转税种核定对象 c_new_customer_tax_type_check
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("新户流转税种核定对象")
@Accessors(chain = true)
@TableName("c_new_customer_tax_type_check")
public class NewCustomerTaxTypeCheck extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 上报类型, 1-月报, 2-季报, 3-年报, 4-次报 */
    @Excel(name = "上报类型, 1-月报, 2-季报, 3-年报, 4-次报")
    @TableField("report_type")
    @ApiModelProperty(value = "上报类型, 1-月报, 2-季报, 3-年报, 4-次报")
    private Integer reportType;

    /** 税种类型 */
    @Excel(name = "税种类型")
    @TableField("tax_type")
    @ApiModelProperty(value = "税种类型")
    private String taxType;


}
