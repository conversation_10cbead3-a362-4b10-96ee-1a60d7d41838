package com.bxm.customer.task;

import com.bxm.customer.service.ICustomerServiceCashierAccountingService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RpaInAccountTask {

//    @Autowired
//    private ICustomerServiceInAccountService customerServiceInAccountService;

    @Autowired
    private ICustomerServiceCashierAccountingService customerServiceCashierAccountingService;

    @XxlJob("rpaInAccountTask")
    public ReturnT<String> rpaInAccountTask(String param) {
        log.info("rpa入账取数任务开始=============");
        customerServiceCashierAccountingService.rpaInAccountTask();
        log.info("rpa入账取数任务结束=============");
        return ReturnT.SUCCESS;
    }
}
