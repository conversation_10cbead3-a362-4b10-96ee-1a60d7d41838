package com.bxm.customer.domain.dto.settlementOrder;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderUploadResultDTO {

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("是否完成")
    private Boolean isComplete;

    @ApiModelProperty("总数据")
    private Long totalDataCount;

    @ApiModelProperty("完成数据")
    private Long successDataCount;
}
