package com.bxm.customer.domain.vo.accoutingCashier;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingCashierModifyVO {

    @ApiModelProperty("账务交付单id")
    private Long accountingCashierId;

    @ApiModelProperty("是否有银行流水，0-否，1-是")
    private Integer hasBankPayment;

    @ApiModelProperty("是否凭票入账，0-否，1-是")
    private Integer hasTicket;

    @ApiModelProperty("材料介质，1-电子，2-纸质，3-无，4-其他，5-银企")
    private Integer materialMedia;

    @ApiModelProperty("材料文件")
    private List<CommonFileVO> materialFiles;

    @ApiModelProperty("回单文件")
    private List<CommonFileVO> receiptFiles;

    @ApiModelProperty("交付要求")
    private String deliverRequire;

    @ApiModelProperty("是否关闭，0-否，1-是")
    private Integer isClose;

    @ApiModelProperty("ddl，yyyy-MM-dd")
    private String ddl;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private String operName;

    @ApiModelProperty(hidden = true)
    private Boolean isCoverFiles;
}
