package com.bxm.customer.domain.dto.materialDeliver;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverDetailDTO {

    @ApiModelProperty("交付单id")
    private Long id;

    @ApiModelProperty("交付单编号")
    private String materialDeliverNumber;

    @ApiModelProperty("交接单类型，1-银行流水，2-普通入账，3-凭票入账")
    private Integer materialDeliverType;

    @ApiModelProperty("交付单类型名称")
    private String materialDeliverTypeStr;

    @ApiModelProperty("解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止")
    private Integer analysisStatus;

    @ApiModelProperty("解析状态名称")
    private String analysisStatusStr;

    @ApiModelProperty("解析结果，1-正常，2-异常")
    private Integer analysisResult;

    @ApiModelProperty("解析结果名称")
    private String analysisResultStr;

    @ApiModelProperty("推送状态，1-待推送，2-已推送")
    private Integer pushStatus;

    @ApiModelProperty("推送状态名称")
    private String pushStatusStr;
}
