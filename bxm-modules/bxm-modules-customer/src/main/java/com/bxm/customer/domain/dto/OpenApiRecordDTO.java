package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenApiRecordDTO {

    @ApiModelProperty("记录id")
    private Long id;

    @ApiModelProperty("通知方")
    @Excel(name = "通知方")
    private String noticeSource;

    @ApiModelProperty("接收方")
    @Excel(name = "通知方")
    private String noticeTarget;

    @ApiModelProperty("通知接口")
    @Excel(name = "通知接口")
    private String noticeFunction;

    @ApiModelProperty("通知内容")
    @Excel(name = "通知内容")
    private String noticeContent;

    @ApiModelProperty("通知结果")
    @Excel(name = "通知结果")
    private String noticeResult;

    @ApiModelProperty("通知时间")
    @Excel(name = "通知时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
