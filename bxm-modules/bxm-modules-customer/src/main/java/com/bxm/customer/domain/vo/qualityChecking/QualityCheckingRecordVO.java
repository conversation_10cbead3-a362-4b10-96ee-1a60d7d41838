package com.bxm.customer.domain.vo.qualityChecking;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QualityCheckingRecordVO extends BaseVO {

    @ApiModelProperty("关键字")
    private String keyWord;

    @ApiModelProperty("客户查询批次号批次号")
    private String batchNo;

    @ApiModelProperty("账期标签是否包含，0-不包含，1-包含")
    private Integer periodTagIncludeFlag;

    @ApiModelProperty("账期标签名称")
    private String periodTagName;

    @ApiModelProperty("账期（开始），yyyyMM")
    private Integer periodStart;

    @ApiModelProperty("账期（结束），yyyyMM")
    private Integer periodEnd;

    @ApiModelProperty("质检类型，1-账务问题，2-风险提示")
    private Integer qualityCheckingType;

    @ApiModelProperty("质检事项id，多个用英文逗号隔开")
    private String qualityCheckingItemIds;

    @ApiModelProperty("质检周期，1-单期，2-累计")
    private Integer qualityCheckingCycle;

    @ApiModelProperty("记录状态，1-进行中，2-已完成，3-超时关闭，4-已关闭，5-失败关闭，多个用逗号隔开")
    private String status;

    @ApiModelProperty("执行结果，1-正常，2-异常")
    private Integer checkingResult;

    @ApiModelProperty("发起时间（开始），yyyy-MM-dd")
    private String createTimeStart;

    @ApiModelProperty("发起时间（结束），yyyy-MM-dd")
    private String createTimeEnd;

    @ApiModelProperty("顾问小组id")
    private Long advisorDeptId;

    @ApiModelProperty("会计小组id")
    private Long accountingDeptId;

    @ApiModelProperty("导出附件类型，1-质检附件")
    private String exportTypes;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true, value = "标签搜索类型，1-不带&，2-带&")
    private Integer periodTagType;

    @ApiModelProperty(hidden = true, value = "标签名称列表")
    private List<String> periodTagNameList;

    @ApiModelProperty(hidden = true, value = "标签数量")
    private Integer periodTagSize;
}
