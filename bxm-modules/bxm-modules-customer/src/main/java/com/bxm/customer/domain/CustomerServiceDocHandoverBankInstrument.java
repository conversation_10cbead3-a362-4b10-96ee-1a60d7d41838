package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 材料交接银行票据对象 c_customer_service_doc_handover_bank_instrument
 * 
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
@ApiModel("材料交接银行票据对象")
@Accessors(chain = true)
@TableName("c_customer_service_doc_handover_bank_instrument")
public class CustomerServiceDocHandoverBankInstrument extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 材料id */
    @Excel(name = "材料id")
    @TableField("customer_service_doc_id")
    @ApiModelProperty(value = "材料id")
    private Long customerServiceDocId;

    /** 银行名称 */
    @Excel(name = "银行名称")
    @TableField("bank_name")
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /** 有无:1-有/0-无 */
    @Excel(name = "有无:1-有/0-无")
    @TableField("has")
    @ApiModelProperty(value = "有无:1-有/0-无")
    private Integer has;

    /** 流水:1-有/0-无 */
    @Excel(name = "流水:1-有/0-无")
    @TableField("has_payment")
    @ApiModelProperty(value = "流水:1-有/0-无")
    private Integer hasPayment;

    /** 对账单:1-有/0-无/-1无法获取 */
    @Excel(name = "对账单:1-有/0-无/-1无法获取")
    @TableField("has_check_ticket")
    @ApiModelProperty(value = "对账单:1-有/0-无/-1无法获取")
    private Integer hasCheckTicket;

    /** 对账单材料介质:1-银企/2-纸质 */
    @Excel(name = "对账单材料介质:1-银企/2-纸质")
    @TableField("check_ticket_content")
    @ApiModelProperty(value = "对账单材料介质:1-银企/2-纸质")
    private Integer checkTicketContent;

    /** 回单:1-有/0-无/-1无法获取 */
    @Excel(name = "回单:1-有/0-无/-1无法获取")
    @TableField("has_back_ticket")
    @ApiModelProperty(value = "回单:1-有/0-无/-1无法获取")
    private Integer hasBackTicket;

    /** 回单材料介质:1-银企/2-纸质 */
    @Excel(name = "回单材料介质:1-银企/2-纸质")
    @TableField("back_ticket_content")
    @ApiModelProperty(value = "回单材料介质:1-银企/2-纸质")
    private Integer backTicketContent;

}
