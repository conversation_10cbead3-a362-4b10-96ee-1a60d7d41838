package com.bxm.customer.domain.dto.docHandover;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/6 15:08
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DocHandoverInstrumentBankDTO {
    @ApiModelProperty(value = "银行名称")
    @NotEmpty
    private String bankName;

    @ApiModelProperty(value = "有无:1-有/0-无")
    @NotNull
    private Integer has;

    @ApiModelProperty(value = "流水:1-有/0-无")
    private Integer hasPayment;

    @ApiModelProperty(value = "对账单:1-有/0-无/-1无法获取")
    private Integer hasCheckTicket;

    @ApiModelProperty(value = "对账单材料介质：返回已勾选的选项，1-勾选了银企，2-勾选了纸质")
    private List<Integer> checkTicketContents;

    @ApiModelProperty("对账单附件")
    private List<CommonFileVO> checkTicketFiles;

    @ApiModelProperty(value = "回单:1-有/0-无/-1无法获取")
    private Integer hasBackTicket;

    @ApiModelProperty(value = "回单材料介质：返回已勾选的选项，1-勾选了银企，2-勾选了纸质")
    private List<Integer> backTicketContents;

    @ApiModelProperty("回单附件")
    private List<CommonFileVO> backTicketFiles;
}
