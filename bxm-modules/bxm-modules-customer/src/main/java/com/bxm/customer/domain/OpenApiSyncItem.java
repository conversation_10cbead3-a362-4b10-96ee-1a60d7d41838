package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 第三方申报同步客户详情对象 c_open_api_sync_item
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
@ApiModel("第三方申报同步客户详情对象")
@Accessors(chain = true)
@TableName("c_open_api_sync_item")
public class OpenApiSyncItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 同步记录id */
    @Excel(name = "同步记录id")
    @TableField("syc_record_id")
    @ApiModelProperty(value = "同步记录id")
    private Long sycRecordId;

    /** 同步客户id */
    @Excel(name = "同步客户id")
    @TableField("sync_customer_id")
    @ApiModelProperty(value = "同步客户id")
    private Long syncCustomerId;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 征收项目 */
    @Excel(name = "征收项目")
    @TableField("item_category_name")
    @ApiModelProperty(value = "征收项目")
    private String itemCategoryName;

    /** 征收品目 */
    @Excel(name = "征收品目")
    @TableField("item_name")
    @ApiModelProperty(value = "征收品目")
    private String itemName;

    /** 实际应纳税额 */
    @Excel(name = "实际应纳税额")
    @TableField("actual_pay_tax_amount")
    @ApiModelProperty(value = "实际应纳税额")
    private String actualPayTaxAmount;

    /** 是否申报 */
    @Excel(name = "是否申报")
    @TableField("is_report")
    @ApiModelProperty(value = "是否申报")
    private String isReport;

    /** 是否缴款 */
    @Excel(name = "是否缴款")
    @TableField("is_paid")
    @ApiModelProperty(value = "是否缴款")
    private String isPaid;

    /** 税款所属期起 */
    @Excel(name = "税款所属期起")
    @TableField("tax_period_start")
    @ApiModelProperty(value = "税款所属期起")
    private String taxPeriodStart;

    /** 税款所属期止 */
    @Excel(name = "税款所属期止")
    @TableField("tax_period_end")
    @ApiModelProperty(value = "税款所属期止")
    private String taxPeriodEnd;

    /** 申报期 */
    @Excel(name = "申报期")
    @TableField("report_period")
    @ApiModelProperty(value = "申报期")
    private String reportPeriod;

    /** 纳税期限 */
    @Excel(name = "纳税期限")
    @TableField("report_type")
    @ApiModelProperty(value = "纳税期限")
    private String reportType;

    /** 申报日期 */
    @Excel(name = "申报日期")
    @TableField("report_date")
    @ApiModelProperty(value = "申报日期")
    private String reportDate;

    /** 缴款日期 */
    @Excel(name = "缴款日期")
    @TableField("pay_date")
    @ApiModelProperty(value = "缴款日期")
    private String payDate;

    /** 申报期限 **/
    @Excel(name = "申报期限")
    @TableField("declaration_deadline")
    @ApiModelProperty(value = "申报期限")
    private String declarationDeadline;

    /** 税款所属期止所在年月 **/
    @Excel(name = "税款所属期止所在年月")
    @TableField("tax_period_end_month")
    @ApiModelProperty(value = "税款所属期止所在年月")
    private String taxPeriodEndMonth;
}
