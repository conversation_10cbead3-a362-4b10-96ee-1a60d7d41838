package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
public class CustomerServiceIncomeDTO {

    @ApiModelProperty("本月收入")
    private BigDecimal incomeThisMonth;

    private String incomeThisMonthStr;

    @ApiModelProperty("本季收入")
    private BigDecimal incomeThisSeason;

    private String incomeThisSeasonStr;

    @ApiModelProperty("本年收入")
    private BigDecimal incomeThisYear;

    private String incomeThisYearStr;

    @ApiModelProperty("近12个月收入")
    private BigDecimal income12Month;

    private String income12MonthStr;

    public CustomerServiceIncomeDTO() {
        this.incomeThisMonth = BigDecimal.ZERO;
        this.incomeThisMonthStr = "0";
        this.incomeThisSeason = BigDecimal.ZERO;
        this.incomeThisSeasonStr = "0";
        this.incomeThisYear = BigDecimal.ZERO;
        this.incomeThisYearStr = "0";
        this.income12Month = BigDecimal.ZERO;
        this.income12MonthStr = "0";
    }
}
