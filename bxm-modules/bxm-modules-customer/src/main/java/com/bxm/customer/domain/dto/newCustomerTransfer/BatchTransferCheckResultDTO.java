package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchTransferCheckResultDTO {

    private String batchNo;

    @ApiModelProperty("是否有异常数据,")
    private Boolean hasException;

    @ApiModelProperty("异常信息")
    private List<String> errorStatistics;

    @ApiModelProperty("档案编号已存在数量，若这个值数量大于0，在下一步时还需要进一步提示")
    private Integer serviceNumberExistCount;

    @ApiModelProperty("数据")
    private List<NewCustomerWaitTransferCheckDataDTO> data;
}
