package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客户服务待确认事项对象 c_customer_service_wait_item
 * 
 * <AUTHOR>
 * @date 2024-05-12
 */
@Data
@ApiModel("客户服务待确认事项对象")
@Accessors(chain = true)
@TableName("c_customer_service_wait_item")
public class CCustomerServiceWaitItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 事项类型，1-会计待分派，2-会计待重派，3-更名待确认 */
    @Excel(name = "事项类型，1-会计待分派，2-会计待重派，3-更名待确认")
    @TableField("item_type")
    @ApiModelProperty(value = "事项类型，1-会计待分派，2-会计待重派，3-更名待确认")
    private Integer itemType;

    /** 事项内容 */
    @Excel(name = "事项内容")
    @TableField("item_content")
    @ApiModelProperty(value = "事项内容")
    private String itemContent;

    /** 是否有效，0-否，1-是 */
    @Excel(name = "是否有效，0-否，1-是")
    @TableField("is_valid")
    @ApiModelProperty(value = "是否有效，0-否，1-是")
    private Boolean isValid;

    /** 处理状态，0-待处理，1-已确认，2-无需处理 */
    @Excel(name = "处理状态，0-待处理，1-已确认，2-无需处理")
    @TableField("done_status")
    @ApiModelProperty(value = "处理状态，0-待处理，1-已确认，2-无需处理")
    private Integer doneStatus;

    @TableField(exist = false)
    private Long advisorDeptId;

    @TableField(exist = false)
    private Long accountingDeptId;
}
