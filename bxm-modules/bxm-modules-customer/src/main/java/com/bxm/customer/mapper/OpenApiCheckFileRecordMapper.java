package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.OpenApiCheckFileRecord;

/**
 * 检验文件定时轮询Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-23
 */
@Mapper
public interface OpenApiCheckFileRecordMapper extends BaseMapper<OpenApiCheckFileRecord>
{
    /**
     * 查询检验文件定时轮询
     * 
     * @param id 检验文件定时轮询主键
     * @return 检验文件定时轮询
     */
    public OpenApiCheckFileRecord selectOpenApiCheckFileRecordById(Long id);

    /**
     * 查询检验文件定时轮询列表
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 检验文件定时轮询集合
     */
    public List<OpenApiCheckFileRecord> selectOpenApiCheckFileRecordList(OpenApiCheckFileRecord openApiCheckFileRecord);

    /**
     * 新增检验文件定时轮询
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 结果
     */
    public int insertOpenApiCheckFileRecord(OpenApiCheckFileRecord openApiCheckFileRecord);

    /**
     * 修改检验文件定时轮询
     * 
     * @param openApiCheckFileRecord 检验文件定时轮询
     * @return 结果
     */
    public int updateOpenApiCheckFileRecord(OpenApiCheckFileRecord openApiCheckFileRecord);

    /**
     * 删除检验文件定时轮询
     * 
     * @param id 检验文件定时轮询主键
     * @return 结果
     */
    public int deleteOpenApiCheckFileRecordById(Long id);

    /**
     * 批量删除检验文件定时轮询
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiCheckFileRecordByIds(Long[] ids);
}
