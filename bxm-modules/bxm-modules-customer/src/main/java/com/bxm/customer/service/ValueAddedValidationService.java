package com.bxm.customer.service;

import com.bxm.customer.domain.CCustomerService;
import com.bxm.customer.domain.CustomerServicePeriodMonth;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedDeliveryOrderVO;
import com.bxm.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 增值交付单校验服务
 *
 * 负责处理不同增值事项名称的特殊校验逻辑：
 * 1. "改账"场景：校验客户服务存在性并更新相关信息
 * 2. "补账"场景：补全缺失的账期数据
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class ValueAddedValidationService {

    @Autowired
    private com.bxm.customer.service.ICCustomerServiceService customerServiceService;

    @Autowired
    private com.bxm.customer.service.ICustomerServicePeriodMonthService customerServicePeriodMonthService;

    /**
     * 根据增值事项名称执行相应的校验逻辑
     *
     * @param orderVO 增值交付单VO
     * @param order 增值交付单实体（用于更新字段信息）
     */
    public void validateByItemName(ValueAddedDeliveryOrderVO orderVO, ValueAddedDeliveryOrder order) {
        if (StringUtils.isEmpty(orderVO.getItemName())) {
            return; // 如果没有指定增值事项名称，跳过特殊校验
        }

        String itemName = orderVO.getItemName().trim();
        log.info("Processing validation for itemName: {}", itemName);

        switch (itemName) {
            case "改账":
                validateAndUpdateForAccountCorrection(orderVO, order);
                break;
            case "补账":
                validateAndSupplementAccountPeriods(orderVO);
                break;
            default:
                log.debug("No special validation required for itemName: {}", itemName);
                break;
        }
    }

    /**
     * 改账场景的校验和更新逻辑
     *
     * @param orderVO 增值交付单VO
     * @param order 增值交付单实体
     */
    private void validateAndUpdateForAccountCorrection(ValueAddedDeliveryOrderVO orderVO, ValueAddedDeliveryOrder order) {
        // 校验 customId 是否提供
        if (orderVO.getCustomId() == null) {
            throw new IllegalArgumentException("改账场景下，客户服务ID(customId)不能为空");
        }

        log.info("Validating customer service existence for customId: {}", orderVO.getCustomId());

        // 查询客户服务是否存在（使用 IService 的 getById 方法）
        CCustomerService customerService = customerServiceService.getById(orderVO.getCustomId());
        if (customerService == null || Boolean.TRUE.equals(customerService.getIsDel())) {
            throw new IllegalArgumentException("指定的客户服务不存在或已被删除，customId: " + orderVO.getCustomId());
        }

        log.info("Customer service found: customerName={}, creditCode={}, updating delivery order fields",
                customerService.getCustomerName(), customerService.getCreditCode());

        // 更新交付单中的相关字段信息，使其与客户服务保持一致
        updateDeliveryOrderFromCustomerService(order, customerService);
    }

    /**
     * 补账场景的校验和账期补全逻辑
     *
     * @param orderVO 增值交付单VO
     */
    @Transactional(rollbackFor = Exception.class)
    private void validateAndSupplementAccountPeriods(ValueAddedDeliveryOrderVO orderVO) {
        // 校验账期范围是否提供
        if (orderVO.getAccountingPeriodStart() == null || orderVO.getAccountingPeriodEnd() == null) {
            throw new IllegalArgumentException("补账场景下，账期开始时间和结束时间不能为空");
        }

        // 校验账期格式和范围
        if (!isValidPeriodFormat(orderVO.getAccountingPeriodStart()) ||
            !isValidPeriodFormat(orderVO.getAccountingPeriodEnd())) {
            throw new IllegalArgumentException("账期格式不正确，应为YYYYMM格式，如：202301");
        }

        // 校验 customerId 是否提供
        if (orderVO.getCustomerId() == null) {
            throw new IllegalArgumentException("补账场景下，客户ID不能为空");
        }

        // 校验客户服务是否存在
        CCustomerService customerService = customerServiceService.getById(orderVO.getCustomerId());
        if (customerService == null || Boolean.TRUE.equals(customerService.getIsDel())) {
            throw new IllegalArgumentException("指定的客户服务不存在或已被删除，customerId: " + orderVO.getCustomerId());
        }

        log.info("Processing account period supplementation for customerId: {}, customerName: {}, period range: {} - {}",
                orderVO.getCustomerId(), customerService.getCustomerName(),
                orderVO.getAccountingPeriodStart(), orderVO.getAccountingPeriodEnd());

        // 检查是否有缺失的账期
        List<Integer> missingPeriods = findMissingPeriods(orderVO.getCustomerId(),
                orderVO.getAccountingPeriodStart(), orderVO.getAccountingPeriodEnd());

        if (!missingPeriods.isEmpty()) {
            log.info("Found {} missing periods to supplement: {}", missingPeriods.size(), missingPeriods);

            // 复用现有的补账方法，使用 saveMonthPeriodListFromOther
            supplementAccountPeriodsUsingExistingMethod(customerService, orderVO.getAccountingPeriodStart(),
                    orderVO.getAccountingPeriodEnd(), orderVO.getId());
        } else {
            log.info("No missing periods found, all periods already exist for customer: {}",
                    customerService.getCustomerName());
        }
    }

    /**
     * 使用现有的补账方法进行账期补全
     * 复用 ICustomerServicePeriodMonthService.saveMonthPeriodListFromOther 方法
     *
     * @param customerService 客户服务
     * @param startPeriod 开始账期
     * @param endPeriod 结束账期
     * @param deliveryOrderId 交付单ID（作为addFromId）
     */
    private void supplementAccountPeriodsUsingExistingMethod(CCustomerService customerService,
            Integer startPeriod, Integer endPeriod, Long deliveryOrderId) {
        try {
            // 准备标签信息（补账服务标签）
            List<com.bxm.customer.domain.dto.TagDTO> tagDTOS = new ArrayList<>();
            // 添加补账标签，ID为6表示补账服务标签
            com.bxm.customer.domain.dto.TagDTO supplementTag = new com.bxm.customer.domain.dto.TagDTO();
            supplementTag.setId(6L); // 补账服务标签ID
            supplementTag.setTagName("补账服务");
            tagDTOS.add(supplementTag);

            // 使用现有的方法创建账期
            // addFromType = 3 表示来自增值交付单
            customerServicePeriodMonthService.saveMonthPeriodListFromOther(
                    customerService,
                    tagDTOS,
                    startPeriod,
                    endPeriod,
                    3, // addFromType: 3-增值交付单
                    deliveryOrderId, // addFromId: 交付单ID
                    customerService.getAccountingDeptId(), // 会计部门ID
                    customerService.getBusinessDeptId(), // 业务部门ID
                    1L, // userId: 系统用户ID
                    "增值交付单补账" // operName: 操作名称
            );

            log.info("Successfully supplemented account periods using existing method for customer: {}, period: {}-{}",
                    customerService.getCustomerName(), startPeriod, endPeriod);

        } catch (Exception e) {
            log.error("Failed to supplement account periods using existing method for customer: {}, period: {}-{}",
                    customerService.getCustomerName(), startPeriod, endPeriod, e);
            throw new RuntimeException("补账失败: " + e.getMessage(), e);
        }
    }

    /**
     * 校验账期格式是否正确
     *
     * @param period 账期
     * @return 是否有效
     */
    private boolean isValidPeriodFormat(Integer period) {
        if (period == null) {
            return false;
        }

        int year = period / 100;
        int month = period % 100;

        // 校验年份范围（2000-2099）和月份范围（1-12）
        return year >= 2000 && year <= 2099 && month >= 1 && month <= 12;
    }

    /**
     * 从客户服务数据更新交付单字段
     *
     * @param order 增值交付单实体
     * @param customerService 客户服务实体
     */
    private void updateDeliveryOrderFromCustomerService(ValueAddedDeliveryOrder order, CCustomerService customerService) {
        // 更新客户相关信息
        order.setCustomerId(customerService.getId());
        order.setCustomerName(customerService.getCustomerName());
        order.setCreditCode(customerService.getCreditCode());
        order.setTaxNo(customerService.getTaxNumber());
        order.setTaxpayerType(customerService.getTaxType());

        // 更新部门信息
        order.setBusinessDeptId(customerService.getBusinessDeptId());
        order.setBusinessTopDeptId(customerService.getBusinessTopDeptId());

        // 设置客户服务ID
        order.setCustomId(customerService.getId());

        log.info("Updated delivery order fields from customer service: customerName={}, creditCode={}, taxNo={}",
                customerService.getCustomerName(), customerService.getCreditCode(), customerService.getTaxNumber());
    }

    /**
     * 查找缺失的账期
     *
     * @param customerServiceId 客户服务ID
     * @param startPeriod 开始账期
     * @param endPeriod 结束账期
     * @return 缺失的账期列表
     */
    private List<Integer> findMissingPeriods(Long customerServiceId, Integer startPeriod, Integer endPeriod) {
        List<Integer> missingPeriods = new ArrayList<>();

        // 生成完整的账期范围
        List<Integer> expectedPeriods = generatePeriodRange(startPeriod, endPeriod);

        // 检查每个账期是否存在
        for (Integer period : expectedPeriods) {
            CustomerServicePeriodMonth existingPeriod = customerServicePeriodMonthService
                    .selectByCustomerServiceIdAndPeriod(customerServiceId, period);
            if (existingPeriod == null) {
                missingPeriods.add(period);
            }
        }

        return missingPeriods;
    }


}
