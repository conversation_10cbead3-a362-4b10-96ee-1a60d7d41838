package com.bxm.customer.domain.vo.workOrder;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderTransmitVO {

    @ApiModelProperty("工单id")
    private Long id;

    @ApiModelProperty("转交类型，1-转交发起人，2-转交承接人")
    private Integer transmitType;

    @ApiModelProperty("转交小组id")
    private Long deptId;

    @ApiModelProperty("转交员工id")
    private Long employeeId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty(hidden = true)
    private Long userId;
}
