package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 新户流转财税信息对象 c_new_customer_finance_tax_info
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Data
@ApiModel("新户流转财税信息对象")
@Accessors(chain = true)
@TableName("c_new_customer_finance_tax_info")
public class NewCustomerFinanceTaxInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 做账系统 */
    @Excel(name = "做账系统")
    @TableField("accounting_system")
    @ApiModelProperty(value = "做账系统")
    private String accountingSystem;

    /** 财务制度是否备案, 0-否, 1-是 */
    @Excel(name = "财务制度是否备案, 0-否, 1-是")
    @TableField("finance_record")
    @ApiModelProperty(value = "财务制度是否备案, 0-否, 1-是")
    private Boolean financeRecord;

    /** 会计报表是否送报, 0-否, 1-是 */
    @Excel(name = "会计报表是否送报, 0-否, 1-是")
    @TableField("report_sent")
    @ApiModelProperty(value = "会计报表是否送报, 0-否, 1-是")
    private Boolean reportSent;

    /** 最后入账月份 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "最后入账月份", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("last_account_month")
    @ApiModelProperty(value = "最后入账月份")
    private LocalDate lastAccountMonth;

    /** 未入账原因 */
    @Excel(name = "未入账原因")
    @TableField("not_account_reason")
    @ApiModelProperty(value = "未入账原因")
    private String notAccountReason;

    /** 其他备注 */
    @Excel(name = "其他备注")
    @TableField("other_remarks")
    @ApiModelProperty(value = "其他备注")
    private String otherRemarks;

    /** 主营收入 */
    @Excel(name = "主营收入")
    @TableField("income_main")
    @ApiModelProperty(value = "主营收入")
    private BigDecimal incomeMain;

    /** 营业外收入 */
    @Excel(name = "营业外收入")
    @TableField("income_other")
    @ApiModelProperty(value = "营业外收入")
    private BigDecimal incomeOther;

    /** 成本 */
    @Excel(name = "成本")
    @TableField("cost")
    @ApiModelProperty(value = "成本")
    private BigDecimal cost;

    /** 费用 */
    @Excel(name = "费用")
    @TableField("expense")
    @ApiModelProperty(value = "费用")
    private BigDecimal expense;

    /** 利润 */
    @Excel(name = "利润")
    @TableField("profit")
    @ApiModelProperty(value = "利润")
    private BigDecimal profit;

    /** 可弥补损益 */
    @Excel(name = "可弥补损益")
    @TableField("offset_loss")
    @ApiModelProperty(value = "可弥补损益")
    private BigDecimal offsetLoss;

    /** 工资总额 */
    @Excel(name = "工资总额")
    @TableField("total_salary")
    @ApiModelProperty(value = "工资总额")
    private BigDecimal totalSalary;

    /** 福利费 */
    @Excel(name = "福利费")
    @TableField("welfare_fee")
    @ApiModelProperty(value = "福利费")
    private BigDecimal welfareFee;

    /** 招待费 */
    @Excel(name = "招待费")
    @TableField("entertainment_fee")
    @ApiModelProperty(value = "招待费")
    private BigDecimal entertainmentFee;

    /** 其他调增项 */
    @Excel(name = "其他调增项")
    @TableField("other_adjustment")
    @ApiModelProperty(value = "其他调增项")
    private BigDecimal otherAdjustment;

    /** 个税申报方式, 1-易捷账, 2-扣缴端 */
    @Excel(name = "个税申报方式, 1-易捷账, 2-扣缴端")
    @TableField("tax_method")
    @ApiModelProperty(value = "个税申报方式, 1-易捷账, 2-扣缴端")
    private Integer taxMethod;

    /** 易捷账个税账号, 0-未维护, 1-已维护 */
    @Excel(name = "易捷账个税账号, 0-未维护, 1-已维护")
    @TableField("ez_tax_account")
    @ApiModelProperty(value = "易捷账个税账号, 0-未维护, 1-已维护")
    private Boolean ezTaxAccount;

    /** 税盘, 1-黑盘, 2-白盘, 3-U-key, 4-无 */
    @Excel(name = "税盘, 1-黑盘, 2-白盘, 3-U-key, 4-无")
    @TableField("tax_disk")
    @ApiModelProperty(value = "税盘, 1-黑盘, 2-白盘, 3-U-key, 4-无")
    private Integer taxDisk;

    /** 税盘密码 */
    @Excel(name = "税盘密码")
    @TableField("tax_password")
    @ApiModelProperty(value = "税盘密码")
    private String taxPassword;

}
