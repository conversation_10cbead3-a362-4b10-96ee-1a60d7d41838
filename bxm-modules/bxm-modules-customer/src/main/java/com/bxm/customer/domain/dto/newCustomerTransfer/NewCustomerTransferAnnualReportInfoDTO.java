package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferAnnualReportInfoDTO {

    @ApiModelProperty("状态，1-已申报，2-未申报")
    private Integer status;

    @ApiModelProperty("电子营业执照，0-无，1-有")
    private Integer eBusinessLicense;

    @ApiModelProperty("联系人姓名")
    private String contactName;

    @ApiModelProperty("联系人手机号")
    private String contactMobile;

    @ApiModelProperty("联系人身份证号")
    private String contactIdNumber;

    @ApiModelProperty("备注")
    private String notes;
}
