package com.bxm.customer.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
public class PeriodCountDTO {

    private Integer period;

    private Long count;

    private Long inAccountCount;

    private Long endAccountCount;

    private Long notInAccountCount;

    private Long notEndAccountCount;

    public PeriodCountDTO() {
        this.count = 0L;
        this.inAccountCount = 0L;
        this.endAccountCount = 0L;
        this.notInAccountCount = 0L;
        this.notEndAccountCount = 0L;
    }
}
