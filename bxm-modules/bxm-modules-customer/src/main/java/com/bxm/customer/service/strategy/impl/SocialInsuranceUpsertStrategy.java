package com.bxm.customer.service.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.strategy.AbstractValueAddedEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 社医保业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class SocialInsuranceUpsertStrategy extends AbstractValueAddedEmployeeUpsertStrategy {

    @Autowired
    private ValueAddedEmployeeMapper valueAddedEmployeeMapper;

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.SOCIAL_INSURANCE.getCode();
    }

    @Override
    public void validateBusinessFields(ValueAddedEmployee employee) {
        // 只验证核心业务逻辑，详细格式验证已通过@Valid注解处理
        // 验证操作类型是否适用于社医保业务
        if (!ValueAddedOperationType.isValidForBizType(ValueAddedBizType.SOCIAL_INSURANCE, employee.getOperationType())) {
            throw new IllegalArgumentException("Invalid operation type for social insurance business: " + employee.getOperationType());
        }

        // 验证应发工资（社医保业务通常需要工资信息）
        if (employee.getGrossSalary() == null || employee.getGrossSalary().doubleValue() < 0) {
            throw new IllegalArgumentException("Gross salary is required and must be non-negative for social insurance");
        }


    }

    @Override
    protected void doPreprocess(ValueAddedEmployee employee) {
        // 如果没有提供社保信息，设置默认值
        if (StringUtils.isEmpty(employee.getSocialInsurance())) {
            employee.setSocialInsurance(getDefaultSocialInsurance());
        }
    }

    @Override
    public ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 社医保业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, employee.getDeliveryOrderNo())
                .eq(ValueAddedEmployee::getIdNumber, employee.getIdNumber())
                .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.SOCIAL_INSURANCE.getCode());

        return valueAddedEmployeeMapper.selectOne(queryWrapper);
    }

    @Override
    protected void doMerge(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 更新社医保特定字段
        if (newEmployee.getGrossSalary() != null) {
            existing.setGrossSalary(newEmployee.getGrossSalary());
        }
        if (newEmployee.getProvidentFundPersonal() != null) {
            existing.setProvidentFundPersonal(newEmployee.getProvidentFundPersonal());
        }
        if (StringUtils.isNotEmpty(newEmployee.getSocialInsurance())) {
            existing.setSocialInsurance(newEmployee.getSocialInsurance());
        }
    }



    /**
     * 获取默认的社保信息
     *
     * @return 默认社保JSON字符串
     */
    private String getDefaultSocialInsurance() {
        return "{\"yang_lao\":true,\"shi_ye\":true,\"gong_shang\":true,\"yi_liao\":true,\"sheng_yu\":true,\"qi_ta\":false}";
    }
}
