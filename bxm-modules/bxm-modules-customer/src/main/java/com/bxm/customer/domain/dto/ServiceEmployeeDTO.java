package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceEmployeeDTO {

    @ApiModelProperty("系统用户id")
    private Long userId;

    @ApiModelProperty("员工姓名")
    private String employeeName;

    @ApiModelProperty("是否绑定企业微信用户")
    private Boolean hasBindWechatUser;
}
