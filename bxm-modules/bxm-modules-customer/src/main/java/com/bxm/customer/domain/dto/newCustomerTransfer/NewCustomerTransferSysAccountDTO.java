package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferSysAccountDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("新户流转id")
    private Long newCustomerTransferId;

    @ApiModelProperty("系统账号类型，0-自定义，1-医保，2-社保，3-公积金，4-个税（工资薪金），5-国税")
    private Integer sysAccountType;

    @ApiModelProperty("系统账号类型名称")
    private String sysAccountTypeName;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("登陆类型")
    private String loginType;

    @ApiModelProperty("实名人员")
    private String contact;

    @ApiModelProperty("实名手机号")
    private String contactMobile;

    @ApiModelProperty("身份证号")
    private String idNumber;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("账号是否同信用代码，0-否，1-是")
    private Boolean isSameWithCreditCode;
}
