package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceNumberRepeatCheckResultDTO {

    @ApiModelProperty("服务id")
    private Long id;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("服务状态")
    private String serviceStatus;
}
