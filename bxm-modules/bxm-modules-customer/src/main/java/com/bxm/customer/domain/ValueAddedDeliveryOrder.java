package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.bxm.common.core.annotation.Excel;
import com.bxm.customer.domain.handler.AccountingInfoTypeHandler;
import com.bxm.customer.domain.vo.valueAdded.AccountingInfoVO;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 增值交付单对象 c_value_added_delivery_order
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ApiModel("增值交付单对象")
@Accessors(chain = true)
@TableName(value = "c_value_added_delivery_order", autoResultMap = true)
public class ValueAddedDeliveryOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 交付单编号 */
    @Excel(name = "交付单编号")
    @TableField("delivery_order_no")
    @ApiModelProperty(value = "交付单编号")
    private String deliveryOrderNo;

    /** 客户ID */
    @Excel(name = "客户ID")
    @TableField("customer_id")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    /** 客户企业名称 */
    @Excel(name = "客户企业名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户企业名称")
    private String customerName;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_no")
    @ApiModelProperty(value = "税号")
    private String taxNo;

    /** 纳税性质，1-小规模纳税人，2-一般纳税人 */
    @Excel(name = "纳税性质")
    @TableField("taxpayer_type")
    @ApiModelProperty(value = "纳税性质，1-小规模纳税人，2-一般纳税人")
    private Integer taxpayerType;

    /** 增值事项 */
    @Excel(name = "增值事项")
    @TableField("value_added_item_type")
    @ApiModelProperty(value = "增值事项")
    private Integer valueAddedItemType;

    /** 账期开始时间 (格式：YYYYMM，如：202301) */
    @Excel(name = "账期开始时间")
    @TableField("accounting_period_start")
    @ApiModelProperty(value = "账期开始时间，格式YYYYMM")
    private Integer accountingPeriodStart;

    /** 账期结束时间 (格式：YYYYMM，如：202310) */
    @Excel(name = "账期结束时间")
    @TableField("accounting_period_end")
    @ApiModelProperty(value = "账期结束时间，格式YYYYMM")
    private Integer accountingPeriodEnd;


    /** 联络人姓名 */
    @Excel(name = "联络人姓名")
    @TableField("contact_name")
    @ApiModelProperty(value = "联络人姓名")
    private String contactName;

    /** 联络人手机号 */
    @Excel(name = "联络人手机号")
    @TableField("contact_mobile")
    @ApiModelProperty(value = "联络人手机号")
    private String contactMobile;

    /** 联络人证件号 */
    @Excel(name = "联络人证件号")
    @TableField("contact_id_number")
    @ApiModelProperty(value = "联络人证件号")
    private String contactIdNumber;

    /** 是否同步手续费，0-否，1-是 */
    @Excel(name = "是否同步手续费")
    @TableField("sync_handling_fee")
    @ApiModelProperty(value = "是否同步手续费，0-否，1-是")
    private Boolean syncHandlingFee;

    /** 账户类型信息 (以JSON格式存储在数据库中) */
    @Excel(name = "账户类型")
    @TableField(value = "account_types", typeHandler = AccountingInfoTypeHandler.class)
    @ApiModelProperty(value = "账户类型信息")
    private AccountingInfoVO accountingInfo;

    /** 交付要求 */
    @Excel(name = "交付要求")
    @TableField("requirements")
    @ApiModelProperty(value = "交付要求")
    private String requirements;

    /** 是否同步改派，0-否，1-是 */
    @Excel(name = "是否同步改派")
    @TableField("sync_reassignment")
    @ApiModelProperty(value = "是否同步改派，0-否，1-是")
    private Boolean syncReassignment;

    /** 是否修改工期，0-否，1-是 */
    @Excel(name = "是否修改工期")
    @TableField("modify_due_date")
    @ApiModelProperty(value = "是否修改工期，0-否，1-是")
    private Boolean modifyDueDate;

    /** 交付截止日期 */
    @Excel(name = "交付截止日期")
    @TableField("ddl")
    @ApiModelProperty(value = "交付截止日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate ddl;

    /** 发起部门ID */
    @Excel(name = "发起部门ID")
    @TableField("initiate_dept_id")
    @ApiModelProperty(value = "发起部门ID")
    private Long initiateDeptId;

    /** 交付状态 */
    @Excel(name = "交付状态")
    @TableField("status")
    @ApiModelProperty(value = "交付状态")
    private String status;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    /** 业务部门id */
    @Excel(name = "业务部门id")
    @TableField("business_dept_id")
    @ApiModelProperty(value = "业务部门id")
    private Long businessDeptId;

    /** 顶级业务部门id */
    @Excel(name = "顶级业务部门id")
    @TableField("business_top_dept_id")
    @ApiModelProperty(value = "顶级业务部门id")
    private Long businessTopDeptId;

    /** 客户服务ID（改账场景使用） */
    @Excel(name = "客户服务ID")
    @TableField("custom_id")
    @ApiModelProperty(value = "客户服务ID，改账场景时使用")
    private Long customId;
}
