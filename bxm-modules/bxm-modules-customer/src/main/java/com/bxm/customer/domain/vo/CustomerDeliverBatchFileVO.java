package com.bxm.customer.domain.vo;

import com.bxm.common.core.enums.DeliverFileType;
import com.bxm.common.core.web.domain.CommonFileVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverBatchFileVO {

    // 交付单id
    private Long deliverId;

    // 文件
    private List<CommonFileVO> files;
}
