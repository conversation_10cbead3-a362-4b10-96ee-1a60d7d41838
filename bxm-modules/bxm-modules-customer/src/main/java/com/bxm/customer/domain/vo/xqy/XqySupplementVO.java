package com.bxm.customer.domain.vo.xqy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XqySupplementVO {

    private String taxNumber;

    private String customerName;

    private String operateName;

    private String batchNo;

    private String reportPeriod;

    private String deliverType;

    private List<XqyFileVO> files;

    private String sourceName;

    private Integer source;

    // 附件类型，2-申报附件，3-扣款附件，13-交付附件
    private Integer fileType;

    private Long deptId;
}
