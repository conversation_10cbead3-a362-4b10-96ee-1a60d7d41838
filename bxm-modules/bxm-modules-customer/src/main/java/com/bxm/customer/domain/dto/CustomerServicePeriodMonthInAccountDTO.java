package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodMonthInAccountDTO {

    @ApiModelProperty("入账交付单id")
    private Long inAccountId;

    @ApiModelProperty("结账状态")
    private String inAccountStatus;

}