package com.bxm.customer.domain.vo.newCustomer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferVO {

    @ApiModelProperty("新户流转id")
    private Long id;

    @ApiModelProperty("档案编号")
    private String serviceNumber;

    @ApiModelProperty("会计区域id")
    private Long accountTopDeptId;
}
