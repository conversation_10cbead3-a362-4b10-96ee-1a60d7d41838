package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.ReportTableTaxConfig;

/**
 * 申报税种对应Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Mapper
public interface ReportTableTaxConfigMapper extends BaseMapper<ReportTableTaxConfig>
{
    /**
     * 查询申报税种对应
     * 
     * @param id 申报税种对应主键
     * @return 申报税种对应
     */
    public ReportTableTaxConfig selectReportTableTaxConfigById(Long id);

    /**
     * 查询申报税种对应列表
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 申报税种对应集合
     */
    public List<ReportTableTaxConfig> selectReportTableTaxConfigList(ReportTableTaxConfig reportTableTaxConfig);

    /**
     * 新增申报税种对应
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 结果
     */
    public int insertReportTableTaxConfig(ReportTableTaxConfig reportTableTaxConfig);

    /**
     * 修改申报税种对应
     * 
     * @param reportTableTaxConfig 申报税种对应
     * @return 结果
     */
    public int updateReportTableTaxConfig(ReportTableTaxConfig reportTableTaxConfig);

    /**
     * 删除申报税种对应
     * 
     * @param id 申报税种对应主键
     * @return 结果
     */
    public int deleteReportTableTaxConfigById(Long id);

    /**
     * 批量删除申报税种对应
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteReportTableTaxConfigByIds(Long[] ids);
}
