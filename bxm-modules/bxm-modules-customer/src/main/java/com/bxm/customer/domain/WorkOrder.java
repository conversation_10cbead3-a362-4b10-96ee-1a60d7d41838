package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工单对象 c_work_order
 * 
 * <AUTHOR>
 * @date 2024-10-23
 */
@Data
@ApiModel("工单对象")
@Accessors(chain = true)
@TableName("c_work_order")
public class WorkOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键id")
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 客户服务id */
    @Excel(name = "客户服务id")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务id")
    private Long customerServiceId;

    /** 工单标题 */
    @Excel(name = "工单标题")
    @TableField("title")
    @ApiModelProperty(value = "工单标题")
    private String title;

    /** 工单类型，1-装订账本，2-找材料，3-对账，4-申报更正，5-申报表，6-做账要求，7-统计资料，8-催账，9-催库存，10-添加实名人员，11-补录初期，12-材料补充，13-账务问题，14-其他提醒，15-改账 */
    @Excel(name = "工单类型")
    @TableField("work_order_type")
    @ApiModelProperty(value = "工单类型，1-装订账本，2-找材料，3-对账，4-申报更正，5-申报表，6-做账要求，7-统计资料，8-催账，9-催库存，10-添加实名人员，11-补录初期，12-材料补充，13-账务问题，14-其他提醒，15-系统需求，16-改账")
    private Integer workOrderType;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** DDL */
    @Excel(name = "DDL")
    @TableField("ddl")
    @ApiModelProperty(value = "DDL")
    private LocalDate ddl;

    @Excel(name = "账期开始")
    @TableField("period_start")
    @ApiModelProperty(value = "账期开始")
    private Integer periodStart;

    @Excel(name = "账期结束")
    @TableField("period_end")
    @ApiModelProperty(value = "账期结束")
    private Integer periodEnd;

    /** 发起用户id */
    @Excel(name = "发起用户id")
    @TableField("initiate_user_id")
    @ApiModelProperty(value = "发起用户id")
    private Long initiateUserId;

    /** 发起用户昵称 */
    @Excel(name = "发起用户昵称")
    @TableField("initiate_user_nickname")
    @ApiModelProperty(value = "发起用户昵称")
    private String initiateUserNickname;

    /** 发起员工id */
    @Excel(name = "发起员工id")
    @TableField("initiate_employee_id")
    @ApiModelProperty(value = "发起员工id")
    private Long initiateEmployeeId;

    /** 发起员工名称 */
    @Excel(name = "发起员工名称")
    @TableField("initiate_employee_name")
    @ApiModelProperty(value = "发起员工名称")
    private String initiateEmployeeName;

    /** 发起小组id */
    @Excel(name = "发起小组id")
    @TableField("initiate_dept_id")
    @ApiModelProperty(value = "发起小组id")
    private Long initiateDeptId;

    /** 承接用户id */
    @Excel(name = "承接用户id")
    @TableField("undertake_user_id")
    @ApiModelProperty(value = "承接用户id")
    private Long undertakeUserId;

    /** 承接用户昵称 */
    @Excel(name = "承接用户昵称")
    @TableField("undertake_user_nickname")
    @ApiModelProperty(value = "承接用户昵称")
    private String undertakeUserNickname;

    /** 承接员工id */
    @Excel(name = "承接员工id")
    @TableField("undertake_employee_id")
    @ApiModelProperty(value = "承接员工id")
    private Long undertakeEmployeeId;

    /** 承接员工名称 */
    @Excel(name = "承接员工名称")
    @TableField("undertake_employee_name")
    @ApiModelProperty(value = "承接员工名称")
    private String undertakeEmployeeName;

    /** 承接小组id */
    @Excel(name = "承接小组id")
    @TableField("undertake_dept_id")
    @ApiModelProperty(value = "承接小组id")
    private Long undertakeDeptId;

    /** 当前处理人类型，1-发起人，2-承接人 */
    @Excel(name = "当前处理人类型，1-发起人，2-承接人")
    @TableField("current_user_type")
    @ApiModelProperty(value = "当前处理人类型，1-发起人，2-承接人")
    private Integer currentUserType;

    /** 当前处理用户id */
    @Excel(name = "当前处理用户id")
    @TableField("current_user_id")
    @ApiModelProperty(value = "当前处理用户id")
    private Long currentUserId;

    /** 当前处理用户昵称 */
    @Excel(name = "当前处理用户昵称")
    @TableField("current_user_nickname")
    @ApiModelProperty(value = "当前处理用户昵称")
    private String currentUserNickname;

    /** 当前处理员工id */
    @Excel(name = "当前处理员工id")
    @TableField("current_employee_id")
    @ApiModelProperty(value = "当前处理员工id")
    private Long currentEmployeeId;

    /** 当前处理员工名称 */
    @Excel(name = "当前处理员工名称")
    @TableField("current_employee_name")
    @ApiModelProperty(value = "当前处理员工名称")
    private String currentEmployeeName;

    /** 当前处理小组id */
    @Excel(name = "当前处理小组id")
    @TableField("current_dept_id")
    @ApiModelProperty(value = "当前处理小组id")
    private Long currentDeptId;

    /** 状态，1-待完结，2-已完结 */
    @Excel(name = "状态，1-待完结，2-已完结，3-超时关闭")
    @TableField("status")
    @ApiModelProperty(value = "状态，1-待完结，2-已完结，3-超时关闭")
    private Integer status;

    /** 最近跟进人 */
    @Excel(name = "最近跟进人")
    @TableField("last_follow_up_name")
    @ApiModelProperty(value = "最近跟进人")
    private String lastFollowUpName;

    /** 最近跟进时间 */
    @Excel(name = "最近跟进时间")
    @TableField("last_follow_up_time")
    @ApiModelProperty(value = "最近跟进时间")
    private LocalDateTime lastFollowUpTime;

    /** 最近操作人 */
    @Excel(name = "最近操作人")
    @TableField("last_oper_name")
    @ApiModelProperty(value = "最近操作人")
    private String lastOperName;

    /** 最近操作类型 */
    @Excel(name = "最近操作类型")
    @TableField("last_oper_type")
    @ApiModelProperty(value = "最近操作类型")
    private String lastOperType;

    /** 最近操作时间 */
    @Excel(name = "最近操作时间")
    @TableField("last_oper_time")
    @ApiModelProperty(value = "最近操作时间")
    private LocalDateTime lastOperTime;

    @TableField("finish_time")
    @ApiModelProperty(value = "完结时间")
    private LocalDateTime finishTime;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;
}
