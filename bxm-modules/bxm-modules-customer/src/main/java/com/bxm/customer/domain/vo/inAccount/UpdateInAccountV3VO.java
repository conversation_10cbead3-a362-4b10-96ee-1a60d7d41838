package com.bxm.customer.domain.vo.inAccount;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/17 17:23
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateInAccountV3VO {
    @ApiModelProperty("入账交付id")
    private Long id;

    @ApiModelProperty(value = "交付结果：1-正常、2-无需交付、3-异常")
    private Integer deliverResult;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "银行流水录入日期")
    private LocalDate bankPaymentInputTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "入账时间")
    private LocalDate inTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最新逻辑：这个字段前端不用传，后端已经不做任何逻辑。结账时间，虽然是通过入账时间和银行流水录入日期，的最大值，计算出来的，但任然需要把这个值传过来，这个值的逻辑就是 在 bankPaymentInputTime 和 inTime 都不为空的情况下，的最大值")
    private LocalDate endTime;

    //****** START 数据
    @ApiModelProperty(value = "本年累计主营收入")
    private BigDecimal majorIncomeTotal;

    @ApiModelProperty(value = "本年累计主营成本")
    private BigDecimal majorCostTotal;

    @ApiModelProperty(value = "本年累计会计利润")
    private BigDecimal profitTotal;

    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;
    //****** END 数据

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    // 以下4个参数是远程调用时传的
    private Long userId;

    @ApiModelProperty("是否要处理附件")
    private Boolean dealFiles;

    //下标
    private Integer index;

    @ApiModelProperty(value = "批量交付填写的交付结果文案")
    private String deliverResultStr;

    @ApiModelProperty(value = "银行流水录入结果，1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺")
    private Integer bankPaymentInputResult;

    @ApiModelProperty(value = "入账结果，1-正常2-异常3-无需交付4-无账务")
    private Integer inAccountResult;

    @ApiModelProperty(value = "批量交付填写的银行流水录入结果文案")
    private String bankPaymentInputResultStr;

    @ApiModelProperty(value = "批量交付填写的入账结果文案")
    private String inAccountResultStr;
}
