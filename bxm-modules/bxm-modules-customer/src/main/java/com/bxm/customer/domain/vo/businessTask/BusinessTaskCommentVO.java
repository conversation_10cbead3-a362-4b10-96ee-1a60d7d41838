package com.bxm.customer.domain.vo.businessTask;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessTaskCommentVO {

    @ApiModelProperty("任务id")
    @NotNull(message = "任务id不能为空")
    private Long id;

    @ApiModelProperty("评论内容")
    private String content;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;
}
