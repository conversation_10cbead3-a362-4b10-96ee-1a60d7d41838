package com.bxm.customer.domain.dto.batchDeliver;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimesReportSupplementReportFileTemplateDTO {

    @Excel(name = "企业名称")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "税种")
    private String taxCheckType;

    @Excel(name = "附件文件名")
    private String fileName;

    @Excel(name = "附件文件夹名")
    private String directName;
}
