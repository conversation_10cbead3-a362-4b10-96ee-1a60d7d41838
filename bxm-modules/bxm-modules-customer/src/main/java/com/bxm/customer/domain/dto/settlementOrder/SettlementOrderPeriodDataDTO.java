package com.bxm.customer.domain.dto.settlementOrder;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderPeriodDataDTO {

    @ApiModelProperty(value = "服务名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty(value = "信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty(value = "服务标签")
    @Excel(name = "服务标签")
    private String customerServiceTags;

    @ApiModelProperty(value = "服务纳税人性质")
    @Excel(name = "服务纳税人性质")
    private String customerServiceTaxTypeStr;

    @ApiModelProperty(value = "服务顾问信息")
    @Excel(name = "服务顾问")
    private String customerServiceAdvisorInfo;

    @ApiModelProperty(value = "服务会计信息")
    @Excel(name = "服务会计")
    private String customerServiceAccountingInfo;

    @ApiModelProperty(value = "账期")
    @Excel(name = "账期")
    private String period;

    @ApiModelProperty(value = "账期类型，1-代账，2-补账")
    @Excel(name = "账期类型")
    private String periodServiceTypeStr;

    @ApiModelProperty(value = "账期纳税人性质")
    @Excel(name = "账期纳税人性质")
    private String periodTaxTypeStr;

    @ApiModelProperty(value = "账期标签")
    @Excel(name = "账期标签")
    private String periodTags;

    @ApiModelProperty(value = "账期顾问信息")
    @Excel(name = "账期顾问")
    private String periodAdvisorInfo;

    @ApiModelProperty(value = "账期会计信息")
    @Excel(name = "账期会计")
    private String periodAccountingInfo;

    @ApiModelProperty(value = "账务状态")
    @Excel(name = "账期账务状态")
    private String periodAccountStatusStr;

//    @ApiModelProperty(value = "入账交付结果")
//    @Excel(name = "入账交付结果")
//    private String periodInAccountDeliverResultStr;

    @ApiModelProperty(value = "银行流水录入结果")
    @Excel(name = "银行流水录入结果")
    private String periodBankPaymentInputResultStr;

    @ApiModelProperty(value = "入账状态")
    @Excel(name = "入账状态")
    private String periodInAccountStatusStr;

    @ApiModelProperty(value = "入账结果")
    @Excel(name = "入账结果")
    private String periodInAccountResultStr;

    @ApiModelProperty(value = "银行流水录入时间")
    @Excel(name = "银行流水录入时间")
    private String periodInAccountBankPaymentInputTime;

    @ApiModelProperty(value = "入账时间")
    @Excel(name = "入账时间")
    private String periodInAccountInTime;

    @ApiModelProperty(value = "结账时间")
    @Excel(name = "结账时间")
    private String periodInAccountEndTime;

    @ApiModelProperty(value = "结算状态")
    private String periodSettlementStatusStr;

    @ApiModelProperty(value = "预收状态")
    private String periodPrepayStatusStr;

//    @ApiModelProperty(value = "服务会计备注")
//    @Excel(name = "会计备注")
//    private String customerServiceAccountingRemark;
//
//    @ApiModelProperty(value = "入账rpa备注")
//    @Excel(name = "RPA备注")
//    private String periodInAccountRpaRemark;
}
