package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.mapper.ValueAddedItemTypeMapper;
import com.bxm.customer.service.IValueAddedItemTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 增值事项类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedItemTypeServiceImpl extends ServiceImpl<ValueAddedItemTypeMapper, ValueAddedItemType>
        implements IValueAddedItemTypeService {

}