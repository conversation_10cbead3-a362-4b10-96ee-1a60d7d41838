package com.bxm.customer.task;

import com.bxm.customer.service.IBusinessTaskService;
import com.bxm.customer.service.INewCustomerInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class WaitInAccountBusinessTaskAutoCloseTask {

    @Autowired
    private IBusinessTaskService businessTaskService;

    @XxlJob("waitInAccountBusinessTaskAutoCloseTask")
    public ReturnT<String> waitInAccountBusinessTaskAutoCloseTask(String param) {
        log.info("待入账处理超时关闭任务开始=============");
        businessTaskService.waitInAccountBusinessTaskAutoCloseTask();
        log.info("待入账处理超时关闭任务结束=============");
        return ReturnT.SUCCESS;
    }
}
