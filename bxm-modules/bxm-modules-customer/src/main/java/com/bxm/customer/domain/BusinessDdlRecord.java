package com.bxm.customer.domain;

import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * ddl修改记录对象 c_business_ddl_record
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@ApiModel("ddl修改记录对象")
@Accessors(chain = true)
@TableName("c_business_ddl_record")
public class BusinessDdlRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 标签id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "标签id")
    @ApiModelProperty(value = "标签id")
    private Long id;

    /** 业务id */
    @Excel(name = "业务id")
    @TableField("business_id")
    @ApiModelProperty(value = "业务id")
    private Long businessId;

    /** 业务类型，和操作记录的业务类型对应 */
    @Excel(name = "业务类型，和操作记录的业务类型对应")
    @TableField("business_type")
    @ApiModelProperty(value = "业务类型，和操作记录的业务类型对应")
    private Integer businessType;

    /** 本次修改的ddl */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "本次修改的ddl", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField("ddl")
    @ApiModelProperty(value = "本次修改的ddl")
    private LocalDate ddl;

}
