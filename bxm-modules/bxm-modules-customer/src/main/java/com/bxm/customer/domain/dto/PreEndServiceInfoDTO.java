package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/13 21:02
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreEndServiceInfoDTO {
    @ApiModelProperty("客户服务ID")
    private Long customerServiceId;

    @ApiModelProperty("当前月 显示文案")
    private String nowMonthStr;

    @ApiModelProperty("当前月 前后端交互值")
    private Integer nowMonth;

    @ApiModelProperty("上月 显示文案")
    private String preMonthStr;

    @ApiModelProperty("上月 前后端交互值")
    private Integer preMonth;

    @ApiModelProperty("首个账期 显示文案")
    private String firstPeriodStr;

    @ApiModelProperty("首个账期 前后端交互值")
    private Integer firstPeriod;

    @ApiModelProperty("当前月是否可选")
    private Boolean nowMonthCanChoose;

    @ApiModelProperty("上月是否可选")
    private Boolean preMonthCanChoose;
}
