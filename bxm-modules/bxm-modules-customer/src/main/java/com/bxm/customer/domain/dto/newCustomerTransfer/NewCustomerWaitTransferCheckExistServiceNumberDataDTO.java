package com.bxm.customer.domain.dto.newCustomerTransfer;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerWaitTransferCheckExistServiceNumberDataDTO {

    @Excel(name = "提交客户名")
    private String customerName;

    @Excel(name = "提交信用代码")
    private String creditCode;

    @Excel(name = "提交档案编号")
    private String serviceNumber;

    @Excel(name = "重复客户名")
    private String existCustomerName;

    @Excel(name = "重复信用代码")
    private String existCreditCode;

    @Excel(name = "重复档案编号")
    private String existServiceNumber;

    @Excel(name = "重复服务状态")
    private String existServiceStatus;

    @Excel(name = "重复客户所属公司")
    private String existBusinessDeptId;
}
