package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.WorkOrderType;

/**
 * 工单类型配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-28
 */
@Mapper
public interface WorkOrderTypeMapper extends BaseMapper<WorkOrderType>
{
    /**
     * 查询工单类型配置
     * 
     * @param id 工单类型配置主键
     * @return 工单类型配置
     */
    public WorkOrderType selectWorkOrderTypeById(Long id);

    /**
     * 查询工单类型配置列表
     * 
     * @param workOrderType 工单类型配置
     * @return 工单类型配置集合
     */
    public List<WorkOrderType> selectWorkOrderTypeList(WorkOrderType workOrderType);

    /**
     * 新增工单类型配置
     * 
     * @param workOrderType 工单类型配置
     * @return 结果
     */
    public int insertWorkOrderType(WorkOrderType workOrderType);

    /**
     * 修改工单类型配置
     * 
     * @param workOrderType 工单类型配置
     * @return 结果
     */
    public int updateWorkOrderType(WorkOrderType workOrderType);

    /**
     * 删除工单类型配置
     * 
     * @param id 工单类型配置主键
     * @return 结果
     */
    public int deleteWorkOrderTypeById(Long id);

    /**
     * 批量删除工单类型配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkOrderTypeByIds(Long[] ids);
}
