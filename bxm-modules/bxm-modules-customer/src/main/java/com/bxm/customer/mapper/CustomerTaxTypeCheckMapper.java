package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerTaxTypeCheck;

/**
 * 客户服务税种核定Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Mapper
public interface CustomerTaxTypeCheckMapper extends BaseMapper<CustomerTaxTypeCheck>
{
    /**
     * 查询客户服务税种核定
     * 
     * @param id 客户服务税种核定主键
     * @return 客户服务税种核定
     */
    public CustomerTaxTypeCheck selectCustomerTaxTypeCheckById(Long id);

    /**
     * 查询客户服务税种核定列表
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 客户服务税种核定集合
     */
    public List<CustomerTaxTypeCheck> selectCustomerTaxTypeCheckList(CustomerTaxTypeCheck customerTaxTypeCheck);

    /**
     * 新增客户服务税种核定
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 结果
     */
    public int insertCustomerTaxTypeCheck(CustomerTaxTypeCheck customerTaxTypeCheck);

    /**
     * 修改客户服务税种核定
     * 
     * @param customerTaxTypeCheck 客户服务税种核定
     * @return 结果
     */
    public int updateCustomerTaxTypeCheck(CustomerTaxTypeCheck customerTaxTypeCheck);

    /**
     * 删除客户服务税种核定
     * 
     * @param id 客户服务税种核定主键
     * @return 结果
     */
    public int deleteCustomerTaxTypeCheckById(Long id);

    /**
     * 批量删除客户服务税种核定
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerTaxTypeCheckByIds(Long[] ids);
}
