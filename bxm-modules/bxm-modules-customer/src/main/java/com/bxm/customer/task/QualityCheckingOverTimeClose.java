package com.bxm.customer.task;

import com.bxm.customer.service.IQualityCheckingResultService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class QualityCheckingOverTimeClose {

    @Autowired
    private IQualityCheckingResultService qualityCheckingResultService;

    @XxlJob("qualityCheckingOverTimeClose")
    public ReturnT<String> qualityCheckingOverTimeClose(String param) {
        log.info("关闭质检超时任务开始=============");
        qualityCheckingResultService.overTimeClose();
        log.info("关闭质检超时任务结束=============");
        return ReturnT.SUCCESS;
    }
}
