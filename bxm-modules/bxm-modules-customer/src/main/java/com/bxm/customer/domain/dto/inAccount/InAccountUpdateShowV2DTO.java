package com.bxm.customer.domain.dto.inAccount;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/17 17:14
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InAccountUpdateShowV2DTO {
    @ApiModelProperty("入账交付id")
    private Long id;

    @ApiModelProperty(value = "交付类型")
    private String typeStr;

    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @Excel(name = "客户名")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    @ApiModelProperty(value = "月度账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty(value = "账期")
    private Integer period;

    @ApiModelProperty(value = "交付结果：1-正常、2-无需交付")
    private Integer deliverResult;

    @Excel(name = "交付结果")
    @ApiModelProperty(value = "交付结果 字符串：1-正常、2-无需交付")
    private String deliverResultStr;

    @ApiModelProperty(value = "入账状态，1未入账、2已入账未结账、3已入账已结账")
    private Integer status;

    @ApiModelProperty(value = "入账状态，字符串")
    private String statusStr;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "银行流水录入日期")
    private LocalDate bankPaymentInputTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "入账时间")
    private LocalDate inTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结账时间")
    private LocalDate endTime;

    @ApiModelProperty(value = "主营收入累计")
    private BigDecimal majorIncomeTotal;

    @ApiModelProperty(value = "主营成本累计")
    private BigDecimal majorCostTotal;

    @ApiModelProperty(value = "利润总计")
    private BigDecimal profitTotal;

    @ApiModelProperty(value = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    @ApiModelProperty(value = "银行流水录入结果，1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺")
    private Integer bankPaymentInputResult;

    @ApiModelProperty(value = "入账结果，1-正常2-异常3-无需交付4-无账务")
    private Integer inAccountResult;
}
