package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/20 22:37
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdatePeriodMonthDTO {
    @ApiModelProperty("月账期ID")
    @NotNull(message = "请提交ID")
    private Long id;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    @NotNull(message = "请选择纳税人性质")
    private Integer taxType;

    @ApiModelProperty("服务标签")
    @NotEmpty(message = "请选择服务标签")
    private List<TagDTO> tags;

    @ApiModelProperty("业务公司ID")
    @NotNull(message = "请选择业务公司")
    private Long advisorTopDeptId;

    @ApiModelProperty("顾问组别ID")
    @NotNull(message = "请选择顾问组别")
    private Long advisorDeptId;

    @ApiModelProperty("服务顾问")
    @NotEmpty(message = "请选择服务顾问")
    private List<UpdatePeriodMonthEmployeeDTO> advisorEmployees;

    @ApiModelProperty("会计区域ID")
    @NotNull(message = "请选择会计区域")
    private Long accountingTopDeptId;

    @ApiModelProperty("会计组别ID")
    @NotNull(message = "请选择会计组别")
    private Long accountingDeptId;

    @ApiModelProperty("服务会计")
    @NotEmpty(message = "请选择服务会计")
    private List<UpdatePeriodMonthEmployeeDTO> accountingEmployees;
}
