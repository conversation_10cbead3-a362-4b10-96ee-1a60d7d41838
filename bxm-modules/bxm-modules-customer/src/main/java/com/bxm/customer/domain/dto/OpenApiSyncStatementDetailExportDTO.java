package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OpenApiSyncStatementDetailExportDTO {

    @Excel(name = "姓名")
    @ApiModelProperty(value = "姓名")
    private String name;

    /** 参保人员身份 */
    @Excel(name = "身份证号码")
    @ApiModelProperty(value = "个人识别号")
    private String personalId;

    @Excel(name = "养老（单位）")
    @ApiModelProperty(value = "养老_单位承担")
    private String pensionCompanyBear;

    @Excel(name = "失业（单位）")
    @ApiModelProperty(value = "失业_单位承担")
    private String unemploymentCompanyBear;

    @Excel(name = "工伤（单位）")
    @ApiModelProperty(value = "工伤_单位承担")
    private String injuryCompanyBear;

    @Excel(name = "医疗（单位）")
    @ApiModelProperty(value = "医疗_单位承担")
    private String medicalCompanyBear;

    @Excel(name = "生育（单位）")
    @ApiModelProperty(value = "生育_单位承担")
    private String maternityCompanyBear;

    @Excel(name = "养老（个人）")
    @ApiModelProperty(value = "养老_个人承担")
    private String pensionPersonalBear;

    @Excel(name = "医疗（个人）")
    @ApiModelProperty(value = "医疗_个人承担")
    private String medicalPersonalBear;

    @Excel(name = "失业（个人）")
    @ApiModelProperty(value = "失业_个人承担")
    private String unemploymentPersonalBear;

    @Excel(name = "个人合计")
    private String totalPersonalBear;

    @Excel(name = "单位合计")
    private String totalCompanyBear;

    @Excel(name = "合计")
    private String totalBear;

}
