package com.bxm.customer.domain.dto.bill;

import com.bxm.common.core.annotation.Excel;
import com.bxm.customer.domain.dto.settlementOrder.SettlementPushReviewOrderDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillDTO {

    @ApiModelProperty("账单id")
    private Long id;

    @ApiModelProperty("账单标题")
    @Excel(name = "账单标题")
    private String billTitle;

    @ApiModelProperty("账单编号")
    @Excel(name = "账单编号")
    private String billNo;

    @ApiModelProperty("业务集团id")
    private Long businessTopDeptId;

    @ApiModelProperty("业务集团名称")
    @Excel(name = "业务集团")
    private String businessTopDeptName;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("业务公司名称")
    @Excel(name = "业务公司")
    private String businessDeptName;

    @ApiModelProperty("结算单数量")
    @Excel(name = "结算单数量")
    private Integer settlementOrderCount;

    @ApiModelProperty("结算单总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("结算单总金额，去零")
    @Excel(name = "结算单总额")
    private String totalPriceStr;

    @ApiModelProperty("预存抵扣金额")
    private BigDecimal deductionPrice;

    @ApiModelProperty("预存抵扣金额，去零")
    @Excel(name = "预存抵扣金额")
    private String deductionPriceStr;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("应支付金额")
    private BigDecimal oughtPrice;

    @ApiModelProperty("应支付金额，去零")
    @Excel(name = "应收总额")
    private String oughtPriceStr;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("推送时间，格式化后的结果")
    @Excel(name = "推送时间")
    private String createTimeStr;

    @ApiModelProperty("状态,0-已推送待确认，1-已驳回，2-已撤回，3-已确认")
    private Integer status;

    @ApiModelProperty("状态，列表上直接展示这个字段即可")
    @Excel(name = "状态")
    private String statusName;

    @ApiModelProperty("结算单")
    private List<SettlementPushReviewOrderDTO> settlementOrders;
}
