package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 补账 附件对象 c_customer_service_repair_account_file
 * 
 * <AUTHOR>
 * @date 2024-07-11
 */
@Data
@ApiModel("补账 附件对象")
@Accessors(chain = true)
@TableName("c_customer_service_repair_account_file")
public class CustomerServiceRepairAccountFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "ID")
    @ApiModelProperty(value = "ID")
    private Long id;

    /** 关联id */
    @Excel(name = "关联id")
    @TableField("customer_service_repair_account_id")
    @ApiModelProperty(value = "关联id")
    private Long customerServiceRepairAccountId;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @TableField("file_url")
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;

    /** 文件名称 */
    @Excel(name = "文件名称")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /** 附件类型:  */
    @Excel(name = "附件类型: ")
    @TableField("file_type")
    @ApiModelProperty(value = "附件类型: ")
    private Integer fileType;

    /** 字附件类型 */
    @Excel(name = "字附件类型")
    @TableField("sub_file_type")
    @ApiModelProperty(value = "字附件类型")
    private String subFileType;

}
