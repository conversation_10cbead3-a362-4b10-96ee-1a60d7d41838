package com.bxm.customer.domain.dto.newCustomerTransfer;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferInsuranceMonthDTO {

    @ApiModelProperty("年月")
    private Integer yearMonth;

    @ApiModelProperty("显示月份")
    private String monthName;

    @ApiModelProperty("状态, 1-待申报，2-待扣款，3-已扣款，4-同上月，用作回显，这里会返回4")
    private Integer status;

    @ApiModelProperty("状态，直接显示用的，这里会把“同上月”处理成真实状态")
    private String statusStr;

    @ApiModelProperty("是否可操作，true-可操作，false-不可操作")
    private Boolean enabled;
}
