package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.SettlementOrder;

/**
 * 结算单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Mapper
public interface SettlementOrderMapper extends BaseMapper<SettlementOrder>
{
    /**
     * 查询结算单
     * 
     * @param id 结算单主键
     * @return 结算单
     */
    public SettlementOrder selectSettlementOrderById(Long id);

    /**
     * 查询结算单列表
     * 
     * @param settlementOrder 结算单
     * @return 结算单集合
     */
    public List<SettlementOrder> selectSettlementOrderList(SettlementOrder settlementOrder);

    /**
     * 新增结算单
     * 
     * @param settlementOrder 结算单
     * @return 结果
     */
    public int insertSettlementOrder(SettlementOrder settlementOrder);

    /**
     * 修改结算单
     * 
     * @param settlementOrder 结算单
     * @return 结果
     */
    public int updateSettlementOrder(SettlementOrder settlementOrder);

    /**
     * 删除结算单
     * 
     * @param id 结算单主键
     * @return 结果
     */
    public int deleteSettlementOrderById(Long id);

    /**
     * 批量删除结算单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSettlementOrderByIds(Long[] ids);
}
