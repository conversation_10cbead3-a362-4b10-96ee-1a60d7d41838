package com.bxm.customer.domain.dto.settlementOrder;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderDataDTO {

    @ApiModelProperty(value = "数据id，删除的时候传这个值")
    private Long id;

    private Integer businessType;

    private Long businessId;

    private Long settlementOrderId;

    @ApiModelProperty(value = "服务id")
    private Long customerServiceId;

    @ApiModelProperty(value = "服务名称")
    private String customerName;

    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    @ApiModelProperty(value = "服务标签")
    private String customerServiceTags;

    @ApiModelProperty(value = "服务纳税人性质,1-小规模，2-一般纳税人")
    private Integer customerServiceTaxType;

    @ApiModelProperty(value = "服务纳税人性质")
    private String customerServiceTaxTypeStr;

    @ApiModelProperty(value = "服务顾问小组id")
    private Long customerServiceAdvisorDeptId;

    @ApiModelProperty(value = "服务顾问小组名称")
    private String customerServiceAdvisorDeptName;

    @ApiModelProperty(value = "服务顾问人员名称")
    private String customerServiceAdvisorEmployeeName;

    @ApiModelProperty(value = "服务顾问信息")
    private String customerServiceAdvisorInfo;

    @ApiModelProperty(value = "服务会计小组id")
    private Long customerServiceAccountingDeptId;

    @ApiModelProperty(value = "服务会计小组名称")
    private String customerServiceAccountingDeptName;

    @ApiModelProperty(value = "服务会计人员名称")
    private String customerServiceAccountingEmployeeName;

    @ApiModelProperty(value = "服务会计信息")
    private String customerServiceAccountingInfo;

    @ApiModelProperty(value = "服务首个账期")
    private String customerServiceFirstAccountPeriod;

    @ApiModelProperty(value = "账期")
    private String period;

    @ApiModelProperty(value = "账期类型，1-代账，2-补账")
    private Integer periodServiceType;

    @ApiModelProperty(value = "账期类型")
    private String periodServiceTypeStr;

    @ApiModelProperty(value = "账期纳税人性质")
    private Integer periodTaxType;

    @ApiModelProperty(value = "账期纳税人性质")
    private String periodTaxTypeStr;

    @ApiModelProperty(value = "账期标签")
    private String periodTags;

    @ApiModelProperty(value = "账期顾问小组id")
    private Long periodAdvisorDeptId;

    @ApiModelProperty(value = "账期顾问小组名称")
    private String periodAdvisorDeptName;

    @ApiModelProperty(value = "账期顾问人员名称")
    private String periodAdvisorEmployeeName;

    @ApiModelProperty(value = "账期顾问信息")
    private String periodAdvisorInfo;

    @ApiModelProperty(value = "账期会计小组id")
    private Long periodAccountingDeptId;

    @ApiModelProperty(value = "账期会计小组名称")
    private String periodAccountingDeptName;

    @ApiModelProperty(value = "账期会计人员名称")
    private String periodAccountingEmployeeName;

    @ApiModelProperty(value = "账期会计信息")
    private String periodAccountingInfo;

    @ApiModelProperty(value = "账务状态,1-正常，2-无需做账")
    private Integer periodAccountStatus;

    @ApiModelProperty(value = "账务状态")
    private String periodAccountStatusStr;

    @ApiModelProperty(value = "入账交付结果,1-正常，2-无需交付，3-异常")
    private Integer periodInAccountDeliverResult;

    @ApiModelProperty(value = "入账交付结果")
    private String periodInAccountDeliverResultStr;

    @ApiModelProperty(value = "银行流水录入结果，1-正常2-异常3-无需交付4-已开户无流水5-未开户6-银行部分缺")
    private Integer periodBankPaymentInputResult;

    @ApiModelProperty(value = "银行流水录入结果")
    private String periodBankPaymentInputResultStr;

    @ApiModelProperty(value = "入账交付状态，1-待交付，2-交付完成，3-交付异常，4-待重提，6-交付待提交")
    private Integer periodInAccountStatus;

    @ApiModelProperty(value = "入账交付状态")
    private String periodInAccountStatusStr;

    @ApiModelProperty(value = "入账结果，1-正常2-异常3-无需交付4-无账务")
    private Integer periodInAccountResult;

    @ApiModelProperty(value = "入账结果")
    private String periodInAccountResultStr;

    @ApiModelProperty(value = "银行流水录入时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate periodInAccountBankPaymentInputTime;

    @ApiModelProperty(value = "入账时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate periodInAccountInTime;

    @ApiModelProperty(value = "结账时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate periodInAccountEndTime;

    @ApiModelProperty(value = "服务会计备注")
    private String customerServiceAccountingRemark;

    @ApiModelProperty(value = "入账rpa备注")
    private String periodInAccountRpaRemark;

    @ApiModelProperty(value = "账期结算状态，1-不可结算，2-待结算，3-结算中，4，5，6-已结算")
    private Integer periodSettlementStatus;

    @ApiModelProperty(value = "结算状态")
    private String periodSettlementStatusStr;

    @ApiModelProperty(value = "账期预收状态，1-未预收，2-预收中，3-已预收")
    private Integer periodPrepayStatus;

    @ApiModelProperty(value = "预收状态")
    private String periodPrepayStatusStr;

    @ApiModelProperty(value = "创建部门id")
    private Long createDeptId;

    @ApiModelProperty(value = "创建部门名称")
    private String createDeptName;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建人信息（组+人）")
    private String createInfo;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
