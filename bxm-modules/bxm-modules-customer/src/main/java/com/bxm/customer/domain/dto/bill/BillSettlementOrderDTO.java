package com.bxm.customer.domain.dto.bill;

import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillSettlementOrderDTO {

    @ApiModelProperty("结算单标题")
    private String settlementOrderTitle;

    @ApiModelProperty("结算单类型，1-入账结算，2-新户预收")
    private Integer settlementType;

    @ApiModelProperty("结算单明细列表")
    private List<SettlementOrderDataDTO> settlementOrderDataList;
}
