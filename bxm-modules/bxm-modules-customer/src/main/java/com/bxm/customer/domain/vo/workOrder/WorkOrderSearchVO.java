package com.bxm.customer.domain.vo.workOrder;

import com.bxm.common.core.web.domain.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderSearchVO extends BaseVO {

    @ApiModelProperty("tab类型，1-我方发起，2-我方承接，3-待承接，4-待处理")
    private Integer tabType;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("状态，1-待完结，2-已完结")
    private Integer status;

    @ApiModelProperty("发起人搜索")
    private String initiateUserNickname;

    @ApiModelProperty("承接人搜索")
    private String undertakeUserNickname;

    @ApiModelProperty("发起方小组id列表，多个,隔开")
    private String initiateDeptIdList;

    @ApiModelProperty("承接方小组id列表，多个,隔开")
    private String undertakeDeptIdList;

    @ApiModelProperty("当前处理方类型，1-我方，2-他方")
    private Integer currentDealUserType;

    @ApiModelProperty("当前处理人搜索")
    private String currentUserNickname;

    @ApiModelProperty("工单类型筛选，多个逗号隔开")
    private String workOrderTypes;

    @ApiModelProperty("发起时间开始,yyyy-MM-dd")
    private String createTimeStart;

    @ApiModelProperty("发起时间结束,yyyy-MM-dd")
    private String createTimeEnd;

    @ApiModelProperty("最近操作人")
    private String lastOperName;

    @ApiModelProperty("最近操作时间开始,yyyy-MM-dd")
    private String lastOperTimeStart;

    @ApiModelProperty("最近操作时间结束,yyyy-MM-dd")
    private String lastOperTimeEnd;

    @ApiModelProperty("ddl开始,yyyy-MM-dd")
    private String ddlStart;

    @ApiModelProperty("ddl结束,yyyy-MM-dd")
    private String ddlEnd;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty("搜索类型，WAIT_DEAL-待处理，WAIT_UNDERTAKE-待承接，INITIATED-已发起")
    private String searchType;
}
