package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.QualityCheckingFile;

/**
 * 质检附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Mapper
public interface QualityCheckingFileMapper extends BaseMapper<QualityCheckingFile>
{
    /**
     * 查询质检附件
     * 
     * @param id 质检附件主键
     * @return 质检附件
     */
    public QualityCheckingFile selectQualityCheckingFileById(Long id);

    /**
     * 查询质检附件列表
     * 
     * @param qualityCheckingFile 质检附件
     * @return 质检附件集合
     */
    public List<QualityCheckingFile> selectQualityCheckingFileList(QualityCheckingFile qualityCheckingFile);

    /**
     * 新增质检附件
     * 
     * @param qualityCheckingFile 质检附件
     * @return 结果
     */
    public int insertQualityCheckingFile(QualityCheckingFile qualityCheckingFile);

    /**
     * 修改质检附件
     * 
     * @param qualityCheckingFile 质检附件
     * @return 结果
     */
    public int updateQualityCheckingFile(QualityCheckingFile qualityCheckingFile);

    /**
     * 删除质检附件
     * 
     * @param id 质检附件主键
     * @return 结果
     */
    public int deleteQualityCheckingFileById(Long id);

    /**
     * 批量删除质检附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQualityCheckingFileByIds(Long[] ids);
}
