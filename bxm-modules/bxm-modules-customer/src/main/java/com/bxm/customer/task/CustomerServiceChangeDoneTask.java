package com.bxm.customer.task;

import com.bxm.customer.service.ICCustomerServiceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CustomerServiceChangeDoneTask {

    @Autowired
    private ICCustomerServiceService customerServiceService;

    @XxlJob("customerServiceChangeDone")
    public ReturnT<String> customerServiceChangeDone(String param) {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("每月服务待办任务开始=============");
        customerServiceService.customerServiceChangeDone(jobParam);
        log.info("每月服务待办任务结束=============");
        return ReturnT.SUCCESS;
    }
}
