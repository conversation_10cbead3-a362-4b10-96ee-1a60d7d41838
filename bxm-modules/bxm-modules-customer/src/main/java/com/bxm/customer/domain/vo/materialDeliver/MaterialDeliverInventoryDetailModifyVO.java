package com.bxm.customer.domain.vo.materialDeliver;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverInventoryDetailModifyVO {

    @ApiModelProperty("单个操作传id")
    private Long id;

    @ApiModelProperty("批量操作传ids")
    private List<Long> ids;

    @ApiModelProperty("客户id,数据下拉接口/bxmCustomer/select/serviceSelectNoDataScope")
    private Long customerServiceId;

    @ApiModelProperty("清单客户名")
    private String customerName;

    @ApiModelProperty("银行名称,数据下拉来源/bxmCustomer/select/customerServiceBankList")
    private String bankName;

    @ApiModelProperty("银行账号,数据下拉来源/bxmCustomer/select/customerServiceBankList")
    private String bankAccountNumber;

    @ApiModelProperty("文件备注")
    private String fileRemark;

    @ApiModelProperty("开始日期,yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startDate;

    @ApiModelProperty("结束日期,yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate endDate;

    @ApiModelProperty("账期,yyyyMM")
    private Integer period;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private String operName;
}
