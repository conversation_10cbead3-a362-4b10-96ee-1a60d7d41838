package com.bxm.customer.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverCheckVO {

    @ApiModelProperty("交付单id")
    private Long id;

    @ApiModelProperty(value = "检查结果，1-正常，2-未申报，3-未扣款")
    private Integer checkResult;

    @ApiModelProperty(value = "本期金额")
    private BigDecimal currentPeriodAmount;

    @ApiModelProperty(value = "滞纳金")
    private BigDecimal overdueAmount;

    @ApiModelProperty(value = "往期金额")
    private BigDecimal supplementAmount;

    /** 申报金额 */
    @ApiModelProperty(value = "申报金额")
    private BigDecimal reportAmount;

    @ApiModelProperty(value = "增值税")
    private BigDecimal valueAddTaxAmount;

    /** 附加税 */
    @ApiModelProperty(value = "附加税")
    private BigDecimal additionalTaxAmount;

    /** 印花税 */
    @ApiModelProperty(value = "印花税")
    private BigDecimal stampDutyTaxAmount;

    /** 其他税 */
    @ApiModelProperty(value = "其他税")
    private BigDecimal otherTaxAmount;

    @ApiModelProperty(value = "税款总额")
    private BigDecimal totalTaxAmount;

    /** 申报备注 */
    @ApiModelProperty(value = "检查备注")
    private String checkRemark;

    @ApiModelProperty("检查相关附件")
    private List<CommonFileVO> checkFiles;
}
