package com.bxm.customer.domain.dto.borrow;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BorrowOrderDTO {

    @ApiModelProperty("借阅单id")
    private Long id;

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("客户企业名")
    @Excel(name = "客户企业名")
    private String customerName;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名称")
    private String customerCompanyName;

    @ApiModelProperty("提交人所在小组id")
    private Long deptId;

    @ApiModelProperty("提交人所在集团id")
    private Long topDeptId;

    @ApiModelProperty("集团类型")
    private Integer deptType;

    @ApiModelProperty("当时提交人所在小组名称")
    private String deptName;

    @ApiModelProperty("提交员工id")
    private String employeeId;

    @ApiModelProperty("当时提交员工名称")
    private String employeeName;

    @ApiModelProperty("提交人信息，展示用这个字段即可")
    @Excel(name = "提交人")
    private String deptInfo;

    @ApiModelProperty("提交时间")
    private LocalDateTime submitTime;

    @ApiModelProperty("提交时间")
    @Excel(name = "提交时间")
    private String submitDate;

    @ApiModelProperty("状态值，0-待出站，1-待归还，2-待确认，3-已确认，4-无需归还，5-待重提")
    private Integer status;

    @ApiModelProperty("状态")
    @Excel(name = "状态")
    private String statusStr;

    @ApiModelProperty("借阅数量")
    @Excel(name = "借阅数量")
    private Integer borrowAmount;

    @ApiModelProperty("备注")
    @Excel(name = "借阅说明")
    private String remark;
}
