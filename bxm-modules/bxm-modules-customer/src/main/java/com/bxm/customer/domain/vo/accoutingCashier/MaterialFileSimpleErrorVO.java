package com.bxm.customer.domain.vo.accoutingCashier;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialFileSimpleErrorVO {

    @ApiModelProperty("文件名称")
    @Excel(name = "文件名称")
    private String fileName;

    private String fileUrl;

    @Excel(name = "材料类型")
    private String fileType;

    @Excel(name = "银行")
    private String bankInfo;

    @Excel(name = "序号")
    private Integer fileNo;

    @Excel(name = "文件备注")
    private String fileRemark;
}
