package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceCountDTO {

    @ApiModelProperty("有效户数")
    private Long validCount;

    @ApiModelProperty("冻结数")
    private Long frozenCount;

    @ApiModelProperty("本月到期数量")
    private Long thisMonthEndCount;

    @ApiModelProperty("待确认更名数量")
    private Long waitConfirmChangeNameCount;

    @ApiModelProperty("待重派数量")
    private Long waitReDispatchCount;

    @ApiModelProperty("超额预警数量")
    private Long excessWarningCount;

    @ApiModelProperty("开票取数异常数量")
    private Long ticketExceptionCount;
}
