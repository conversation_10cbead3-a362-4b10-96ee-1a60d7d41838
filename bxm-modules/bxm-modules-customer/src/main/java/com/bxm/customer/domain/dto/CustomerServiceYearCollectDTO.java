package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/6/12 13:12
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceYearCollectDTO {
    @ApiModelProperty("个税（工资薪金）申报总额")
    private BigDecimal personalIncomeTaxReportDTotal;

    @ApiModelProperty("医保申报汇总")
    private BigDecimal medicalInsuranceReportTotal;

    @ApiModelProperty("社保申报汇总")
    private BigDecimal socialSecurityReportTotal;

    @ApiModelProperty("全年房屋租赁合同金额")
    private BigDecimal yearHouseRentMoneyTotal;

    @ApiModelProperty("国税申报汇总")
    private BigDecimal nationalTaxReportTotal;

    @ApiModelProperty("主营收入累计")
    private BigDecimal mainIncome;

    @ApiModelProperty("主营成本累计")
    private BigDecimal mainCost;

    @ApiModelProperty("利润累计")
    private BigDecimal profit;

    @ApiModelProperty("最后结账日期")
    private String lastBillingMonth;

    @ApiModelProperty("最后入账日期")
    private String lastAccountingMonth;
}
