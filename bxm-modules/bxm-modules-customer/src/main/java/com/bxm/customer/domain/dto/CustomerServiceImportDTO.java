package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceImportDTO {

    @Excel(name = "客户名")
    private String customerCompanyName;

    @Excel(name = "客户企业名")
    private String customerName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "档案编号")
    private String serviceNumber;

    @Excel(name = "id")
    private String id;

    @Excel(name = "纳税人性质")
    private String taxType;

    @Excel(name = "VIP")
    private String vip;

    @Excel(name = "三零")
    private String sanling;

    @Excel(name = "0申报")
    private String lingshenbao;

    @Excel(name = "医保")
    private String yibao;

    @Excel(name = "社保")
    private String shebao;

    @Excel(name = "公积金")
    private String gongjijin;

    @Excel(name = "工资表")
    private String gongzibiao;

    @Excel(name = "凭票入账")
    private String pingpiaoruzhang;

    @Excel(name = "次月15号出账")
    private String chuzhang15;

    @Excel(name = "次月末出账")
    private String chuzhangLast;

    @Excel(name = "收购票/二手车")
    private String guoshoupiaoAndErshouche;

    @Excel(name = "无票收入")
    private String wupiaoshouru;

    @Excel(name = "差额征收")
    private String chaezhengshou;

    @Excel(name = "简易计税")
    private String jianyijishui;

    @Excel(name = "税负（%）")
    private String shuifu;

    @Excel(name = "甲方")
    private String jiafang;

    @Excel(name = "顾问小组")
    private String advisorDeptName;

    @Excel(name = "顾问")
    private String advisorEmployeeName;

    @Excel(name = "会计区域")
    private String accountingTopDeptName;

    @Excel(name = "会计部门")
    private String accountingDeptName;

    @Excel(name = "会计")
    private String accountingEmployeeName;

    @Excel(name = "开始账期")
    private String startPeriod;

    @Excel(name = "结束时间")
    private String endTime;

    @Excel(name = "增值税")
    private String zengzhishui;

    @Excel(name = "企业所得税")
    private String qiyesuodeshui;

    @Excel(name = "教育费附加税")
    private String jiaoyufeifujiashui;

    @Excel(name = "地方教育附加税")
    private String dianfangjiaoyufeifujiashui;

    @Excel(name = "城市维护建设税")
    private String chengshiweihuanjianshejiaoshui;

    @Excel(name = "个人所得税")
    private String gerensuodeshui;

    @Excel(name = "水利建设专项收入")
    private String shuilijianshezhuanxiangshouru;

    @Excel(name = "残疾人就业保障金")
    private String canjirenjiuyebaozhangjin;

    @Excel(name = "印花税")
    private String yinhuashui;

    @Excel(name = "房产税")
    private String fangchanshui;

    @Excel(name = "其他收入")
    private String qitashouru;
    @Excel(name = "城镇土地使用税")
    private String chengzhentudishiyongshui;
    @Excel(name = "文化事业建设费")
    private String wenhuashiyejianshefei;
    @Excel(name = "消费税")
    private String xiaofeishui;
    @Excel(name = "个人所得税-其他")
    private String gerensuodeshuiJingyingsuode;

    @Excel(name = "国税账号")
    private String guoshuizhanghao;
    @Excel(name = "国税密码")
    private String guoshuimima;
    @Excel(name = "国税登录方式")
    private String guoshuiLoginType;
    @Excel(name = "国税实名/经办人")
    private String guoshuiContact;
    @Excel(name = "国税手机号")
    private String guoshuiPhone;
    @Excel(name = "国税身份证")
    private String guoshuiIdNumber;
    @Excel(name = "国税备注")
    private String guoshuiRemark;

    @Excel(name = "个税账号")
    private String geshuizhanghao;
    @Excel(name = "个税密码")
    private String geshuimima;
    @Excel(name = "个税登录方式")
    private String geshuiLoginType;
    @Excel(name = "个税实名/经办人")
    private String geshuiContact;
    @Excel(name = "个税手机号")
    private String geshuiPhone;
    @Excel(name = "个税身份证")
    private String geshuiIdNumber;
    @Excel(name = "个税备注")
    private String geshuiRemark;

    @Excel(name = "医保账号")
    private String yibaozhanghao;
    @Excel(name = "医保密码")
    private String yibaomima;
    @Excel(name = "医保登录方式")
    private String yibaoLoginType;
    @Excel(name = "医保实名/经办人")
    private String yibaoContact;
    @Excel(name = "医保手机号")
    private String yibaoPhone;
    @Excel(name = "医保身份证")
    private String yibaoIdNumber;
    @Excel(name = "医保备注")
    private String yibaoRemark;

    @Excel(name = "社保账号")
    private String shebaozhanghao;
    @Excel(name = "社保密码")
    private String shebaomima;
    @Excel(name = "社保登录方式")
    private String shebaoLoginType;
    @Excel(name = "社保实名/经办人")
    private String shebaoContact;
    @Excel(name = "社保手机号")
    private String shebaoPhone;
    @Excel(name = "社保身份证")
    private String shebaoIdNumber;
    @Excel(name = "社保备注")
    private String shebaoRemark;

    @Excel(name = "起始账期")
    private String sPeriod;

    @Excel(name = "结束账期")
    private String endPeriod;



    private String error;
}
