package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2024/6/12 19:46
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceMonthPeriodItemDTO {
    @ApiModelProperty(value = "交付单ID")
    private Long customerDeliverId;

    @ApiModelProperty(value = "展示文案，待创建、-、状态")
    private String text;

    @ApiModelProperty(value = "金额")
    private BigDecimal money;

    @ApiModelProperty(value = "展示文案，待创建、-、状态 + 金额")
    private String textFull;

    //none数据
    public static CustomerServiceMonthPeriodItemDTO none() {
        return CustomerServiceMonthPeriodItemDTO.builder()
                .customerDeliverId(null)
                .money(null)
                .text("-")
                .textFull("-")
                .build();
    }
}
