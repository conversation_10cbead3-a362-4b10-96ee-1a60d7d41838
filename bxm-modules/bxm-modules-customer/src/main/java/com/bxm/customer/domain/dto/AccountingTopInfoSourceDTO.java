package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/5 23:01
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingTopInfoSourceDTO {
    @ApiModelProperty(value = "月度账期id")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty("服务会计区域区域名称")
    private String customerServiceAccountingTopDeptName;

    @ApiModelProperty("服务会计区域名称")
    private String periodAccountingTopDeptName;
}
