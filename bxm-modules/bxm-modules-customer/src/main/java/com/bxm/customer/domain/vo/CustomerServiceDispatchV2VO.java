package com.bxm.customer.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceDispatchV2VO {

    @ApiModelProperty("选中的服务id列表")
    private List<Long> ids;

    @ApiModelProperty("影响范围，1-服务，2-账期")
    private Integer dispatchTarget;

    @ApiModelProperty("生效账期类型，1-指定账期，2-全账期")
    private Integer validPeriodType;

    @ApiModelProperty("起始账期，当影响范围=服务且生效账期类型=指定账期时，传这个值；当影响范围=账期，这个值传选择的开始账期")
    private Integer startPeriod;

    @ApiModelProperty("结束账期，当影响范围=账期，这个值传选择的结束账期")
    private Integer endPeriod;

    @ApiModelProperty("目标会计小组id")
    private Long deptId;
}
