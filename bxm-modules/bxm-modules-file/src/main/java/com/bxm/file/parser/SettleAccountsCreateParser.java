package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.SettleAccountsCreateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class SettleAccountsCreateParser implements ExcelV2Parser<SettleAccountsCreateData> {

    @Override
    public List<SettleAccountsCreateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, SettleAccountsCreateData.class);
    }
}
