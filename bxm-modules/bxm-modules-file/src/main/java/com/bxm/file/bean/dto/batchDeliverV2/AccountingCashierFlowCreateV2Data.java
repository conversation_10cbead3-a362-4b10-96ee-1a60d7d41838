package com.bxm.file.bean.dto.batchDeliverV2;

import com.bxm.common.core.annotation.Excel;
import com.bxm.file.bean.dto.AliFileDTO;

import java.util.List;

public class AccountingCashierFlowCreateV2Data implements EnterpriseV2Data {

    @Excel(name = "企业名")
    private String enterpriseName;

    @Excel(name = "信用代码")
    private String creditCode;

    @Excel(name = "银行账号")
    private String bankAccountNumber;

    @Excel(name = "账期：yyyymm")
    private String period;

    @Excel(name = "是否有流水：是/否")
    private String hasBankPayment;

    @Excel(name = "材料介质：电子、纸质、无、其他、银企")
    private String materialMedia;

    @Excel(name = "对账单文件名")
    private String checkFileName;

    @Excel(name = "对账单文件夹名")
    private String checkDirectName;

    @Excel(name = "回单文件名")
    private String receiptFileName;

    @Excel(name = "回单文件夹名")
    private String receiptDirectName;

    @Excel(name = "交付要求")
    private String remark;

    @Excel(name = "DDL（期望完成时间）")
    private String ddl;

    public String getDdl() {
        return ddl;
    }

    public void setDdl(String ddl) {
        this.ddl = ddl;
    }

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> medicalFiles;

    private List<AliFileDTO> socialFiles;

    private AliFileDTO checkFile;

    private AliFileDTO receiptFile;

    private List<AliFileDTO> checkFiles;

    private List<AliFileDTO> receiptFiles;

    private Long customerServiceId;

    private Long periodId;

    private Long medicalDeliverId;

    private Long socialDeliverId;

    private Boolean doMedical;

    private Boolean doSocial;

    private Boolean isMedical;

    private Boolean isSocial;

    private String medicalCheckError;

    private String socialCheckError;

    private String bankName;

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    @Override
    public String getMedicalCheckError() {
        return medicalCheckError;
    }

    @Override
    public String getSocialCheckError() {
        return socialCheckError;
    }

    @Override
    public boolean hasMedicalErrors() {
        return medicalCheckError != null && !medicalCheckError.isEmpty();
    }

    @Override
    public void addMedicalCheckError(String checkError) {
        if (this.medicalCheckError == null || this.medicalCheckError.isEmpty()) {
            this.medicalCheckError = checkError;
        } else {
            this.medicalCheckError += "; " + checkError;
        }
    }

    @Override
    public boolean hasSocialErrors() {
        return socialCheckError != null && !socialCheckError.isEmpty();
    }

    @Override
    public void addSocialCheckError(String checkError) {
        if (this.socialCheckError == null || this.socialCheckError.isEmpty()) {
            this.socialCheckError = checkError;
        } else {
            this.socialCheckError += "; " + checkError;
        }
    }

    @Override
    public Boolean getIsMedical() {
        return isMedical;
    }

    @Override
    public void setIsMedical(Boolean medical) {
        isMedical = medical;
    }

    @Override
    public Boolean getIsSocial() {
        return isSocial;
    }

    @Override
    public void setIsSocial(Boolean social) {
        isSocial = social;
    }

    @Override
    public Boolean getDoMedical() {
        return doMedical;
    }

    @Override
    public void setDoMedical(Boolean doMedical) {
        this.doMedical = doMedical;
    }

    @Override
    public Boolean getDoSocial() {
        return doSocial;
    }

    @Override
    public void setDoSocial(Boolean doSocial) {
        this.doSocial = doSocial;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public List<AliFileDTO> getMedicalFiles() {
        return medicalFiles;
    }

    public void setMedicalFiles(List<AliFileDTO> medicalFiles) {
        this.medicalFiles = medicalFiles;
    }

    @Override
    public List<AliFileDTO> getSocialFiles() {
        return socialFiles;
    }

    public void setSocialFiles(List<AliFileDTO> socialFiles) {
        this.socialFiles = socialFiles;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    @Override
    public Long getMedicalDeliverId() {
        return medicalDeliverId;
    }

    public void setMedicalDeliverId(Long medicalDeliverId) {
        this.medicalDeliverId = medicalDeliverId;
    }

    @Override
    public Long getSocialDeliverId() {
        return socialDeliverId;
    }

    public void setSocialDeliverId(Long socialDeliverId) {
        this.socialDeliverId = socialDeliverId;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getBankAccountNumber() {
        return bankAccountNumber;
    }

    public void setBankAccountNumber(String bankAccountNumber) {
        this.bankAccountNumber = bankAccountNumber;
    }

    public String getHasBankPayment() {
        return hasBankPayment;
    }

    public void setHasBankPayment(String hasBankPayment) {
        this.hasBankPayment = hasBankPayment;
    }

    public String getMaterialMedia() {
        return materialMedia;
    }

    public void setMaterialMedia(String materialMedia) {
        this.materialMedia = materialMedia;
    }

    public String getCheckFileName() {
        return checkFileName;
    }

    public void setCheckFileName(String checkFileName) {
        this.checkFileName = checkFileName;
    }

    public String getCheckDirectName() {
        return checkDirectName;
    }

    public void setCheckDirectName(String checkDirectName) {
        this.checkDirectName = checkDirectName;
    }

    public AliFileDTO getCheckFile() {
        return checkFile;
    }

    public void setCheckFile(AliFileDTO checkFile) {
        this.checkFile = checkFile;
    }

    public AliFileDTO getReceiptFile() {
        return receiptFile;
    }

    public void setReceiptFile(AliFileDTO receiptFile) {
        this.receiptFile = receiptFile;
    }

    public List<AliFileDTO> getCheckFiles() {
        return checkFiles;
    }

    public void setCheckFiles(List<AliFileDTO> checkFiles) {
        this.checkFiles = checkFiles;
    }

    public List<AliFileDTO> getReceiptFiles() {
        return receiptFiles;
    }

    public void setReceiptFiles(List<AliFileDTO> receiptFiles) {
        this.receiptFiles = receiptFiles;
    }

    public String getReceiptFileName() {
        return receiptFileName;
    }

    public void setReceiptFileName(String receiptFileName) {
        this.receiptFileName = receiptFileName;
    }

    public String getReceiptDirectName() {
        return receiptDirectName;
    }

    public void setReceiptDirectName(String receiptDirectName) {
        this.receiptDirectName = receiptDirectName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
