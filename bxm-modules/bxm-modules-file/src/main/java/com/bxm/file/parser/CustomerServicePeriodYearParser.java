package com.bxm.file.parser;

import com.bxm.file.bean.dto.CustomerServicePeriodYearUpdateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CustomerServicePeriodYearParser implements ExcelParser<CustomerServicePeriodYearUpdateData> {
    @Override
    public List<CustomerServicePeriodYearUpdateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CustomerServicePeriodYearUpdateData.class);
    }
}
