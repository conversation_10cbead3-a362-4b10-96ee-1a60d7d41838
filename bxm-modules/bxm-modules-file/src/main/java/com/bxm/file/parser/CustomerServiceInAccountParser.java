package com.bxm.file.parser;

import com.bxm.file.bean.dto.CustomerServiceInAccountData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CustomerServiceInAccountParser implements ExcelParser<CustomerServiceInAccountData> {
    @Override
    public List<CustomerServiceInAccountData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CustomerServiceInAccountData.class);
    }
}
