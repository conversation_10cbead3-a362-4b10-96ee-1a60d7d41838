package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.BusinessTaskBankCreateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class BusinessTaskBankCreateParser implements ExcelV2Parser<BusinessTaskBankCreateData> {
    @Override
    public List<BusinessTaskBankCreateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, BusinessTaskBankCreateData.class);
    }
}
