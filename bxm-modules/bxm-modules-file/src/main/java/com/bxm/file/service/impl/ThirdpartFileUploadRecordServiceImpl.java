package com.bxm.file.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.file.domain.ThirdpartFileUploadRecord;
import com.bxm.file.mapper.ThirdpartFileUploadRecordMapper;
import com.bxm.file.service.IThirdpartFileUploadRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 第三方文件上传记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class ThirdpartFileUploadRecordServiceImpl extends ServiceImpl<ThirdpartFileUploadRecordMapper, ThirdpartFileUploadRecord> implements IThirdpartFileUploadRecordService
{
    @Autowired
    private ThirdpartFileUploadRecordMapper thirdpartFileUploadRecordMapper;

}
