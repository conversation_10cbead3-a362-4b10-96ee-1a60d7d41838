package com.bxm.file.bean.dto.batchDeliverV2;

import com.bxm.file.bean.dto.AliFileDTO;

import java.util.List;

public interface EnterpriseV2Data {

    boolean hasErrors();

    void setCheckError(String checkError);

    void addCheckError(String checkError);

    String getCheckError();

    List<AliFileDTO> getMedicalFiles();

    List<AliFileDTO> getSocialFiles();

    String getCreditCode();

    void setCreditCode(String creditCode);

    Long getMedicalDeliverId();

    Long getSocialDeliverId();

    Boolean getDoMedical();

    Boolean getDoSocial();

    void setDoMedical(Boolean doMedical);

    void setDoSocial(Boolean doSocial);

    Boolean getIsMedical();

    Boolean getIsSocial();

    void setIsMedical(Boolean isMedical);

    void setIsSocial(Boolean isSocial);

    boolean hasMedicalErrors();

    boolean hasSocialErrors();

    void addMedicalCheckError(String checkError);

    void addSocialCheckError(String checkError);

    String getMedicalCheckError();

    String getSocialCheckError();
}
