<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bxm</groupId>
        <artifactId>bxm-common</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>bxm-common-redis</artifactId>
	<version>${bxm.common.redis.version}</version>
    <description>
        bxm-common-redis缓存服务
    </description>

    <dependencies>
		
        <!-- SpringBoot Boot Redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        
        <!-- RuoYi Common Core-->
        <dependency>
            <groupId>com.bxm</groupId>
            <artifactId>bxm-common-core</artifactId>
            <version>${bxm.common.core.version}</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>http://*************:18081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://*************:18081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>