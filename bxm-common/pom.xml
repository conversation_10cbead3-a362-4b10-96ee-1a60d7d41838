<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.bxm</groupId>
        <artifactId>bxm</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>bxm-common-log</module>
        <module>bxm-common-core</module>
        <module>bxm-common-redis</module>
        <module>bxm-common-seata</module>
        <module>bxm-common-swagger</module>
        <module>bxm-common-security</module>
        <module>bxm-common-datascope</module>
        <module>bxm-common-datasource</module>
        <module>bxm-common-mybatisplus</module>
        <module>bxm-common-rocketmq</module>
        <module>bxm-common-customize</module>
    </modules>

    <artifactId>bxm-common</artifactId>
    <packaging>pom</packaging>

    <description>
        bxm-common通用模块
    </description>

    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>http://*************:18081/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://*************:18081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
