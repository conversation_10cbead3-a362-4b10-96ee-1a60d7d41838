package com.bxm.common.core.web.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonFileVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("文件名称")
    private String fileName;

    @ApiModelProperty("文件链接")
    private String fileUrl;

    @ApiModelProperty("完整的文件链接")
    private String fullFileUrl;

    @ApiModelProperty("交付单文件类型")
    private Integer deliverFileType;

    @ApiModelProperty("文件大小")
    private long fileSize;

    private String officalFilename;

    private String baseDir;

    private String fileNumber;

    private String fileRemark;

    private String originFileUrl;
}
