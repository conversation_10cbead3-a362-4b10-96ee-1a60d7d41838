package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum DeptAccountBalanceDetailStatus {

    // 状态，1-待确认，2-已取消，3-已确认
    UNCONFIRMED(1, "待确认"),
    CANCELED(2, "已取消"),
    CONFIRMED(3, "已确认"),

    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String desc;

    public static DeptAccountBalanceDetailStatus getByCode(Integer code) {
        for (DeptAccountBalanceDetailStatus status : DeptAccountBalanceDetailStatus.values()) {
            if (Objects.equals(status.getCode(),code)) {
                return status;
            }
        }
        return UN_KNOW;
    }
}
