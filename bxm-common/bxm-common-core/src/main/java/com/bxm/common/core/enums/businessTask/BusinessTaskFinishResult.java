package com.bxm.common.core.enums.businessTask;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/25 19:01
 * happy coding!
 */
@Getter
@AllArgsConstructor
public enum BusinessTaskFinishResult {
    UN_KNOW(-1, "未知"),

    OK(1, "正常完成"),
    _2(2, "无流水"),
    _3(3, "未开户"),
    _4(4, "银行部分缺"),
    _5(5, "无需交付"),
    _6(6, "无法完成"),
    ;

    private final Integer code;

    private final String name;

    public static BusinessTaskFinishResult getByCode(Integer source) {
        for (BusinessTaskFinishResult item : BusinessTaskFinishResult.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }

    public static List<String> getAllFinishResultNames() {
        return Arrays.asList(OK.getName(), _2.getName(), _5.getName(), _6.getName());
    }

    public static BusinessTaskFinishResult getByName(String finishResult) {
        for (BusinessTaskFinishResult item : BusinessTaskFinishResult.values()) {
            if (item.getName().equals(finishResult)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
