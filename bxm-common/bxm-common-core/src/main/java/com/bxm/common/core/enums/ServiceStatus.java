package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ServiceStatus {

    SERVICE(1, "服务中"),
    END(2, "已结束"),
    FROZE(3, "冻结中"),
    UNKNOW(99, "未知"),
    ;

    private final Integer code;

    private final String desc;

    public static ServiceStatus getServiceStatusByCode(Integer serviceStatus) {
        for (ServiceStatus status : ServiceStatus.values()) {
            if (status.getCode().equals(serviceStatus)) {
                return status;
            }
        }
        return UNKNOW;
    }
}
