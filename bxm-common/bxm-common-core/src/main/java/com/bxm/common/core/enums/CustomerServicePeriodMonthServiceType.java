package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomerServicePeriodMonthServiceType {

    // 1-代账，2-补账
    DO_ACCOUNT(1, "记账"),
    REPAIR_ACCOUNT(2, "补账"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;

    private final String name;

    public static CustomerServicePeriodMonthServiceType getByCode(Integer code) {
        for (CustomerServicePeriodMonthServiceType type : CustomerServicePeriodMonthServiceType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return UN_KNOW;
    }
}
