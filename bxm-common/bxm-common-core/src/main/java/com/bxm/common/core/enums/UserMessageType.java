package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum UserMessageType {

    // 消息类型，1-催办消息，2-系统消息
    ACTIVE_URGE(1, "催办通知"),
    SYSTEM_TRIGGER(2, "系统通知"),
    ;

    private final Integer code;

    private final String name;

    public static String getName(Integer code) {
        for (UserMessageType c : UserMessageType.values()) {
            if (c.getCode().equals(code)) {
                return c.getName();
            }
        }
        return "";
    }
}
