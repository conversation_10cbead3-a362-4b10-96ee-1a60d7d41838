CREATE TABLE `sys_user_message` (
                         `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '标签id',
                         `send_employee_id` BIGINT NULL DEFAULT NULL COMMENT '发送者员工id',
                         `send_employee_name` VARCHAR(64) NULL DEFAULT NULL COMMENT '发送者员工名称',
                         `send_dept_id` BIGINT NULL DEFAULT NULL COMMENT '发送者部门id',
                         `send_dept_name` VARCHAR(64) NULL DEFAULT NULL COMMENT '发送者部门名称',
                         `receive_user_id` BIGINT NOT NULL COMMENT '接收用户id',
                         `receive_employee_id` BIGINT NULL DEFAULT NULL COMMENT '接收者员工id',
                         `receive_employee_name` VARCHAR(64) NULL DEFAULT NULL COMMENT '接收者员工名称',
                         `receive_dept_id` BIGINT NULL DEFAULT NULL COMMENT '接收者部门id',
                         `receive_dept_name` VARCHAR(64) NULL DEFAULT NULL COMMENT '接收者部门名称',
                         `content` VARCHAR(255) NOT NULL COMMENT '消息内容',
                         `message_type` TINYINT NOT NULL DEFAULT '1' COMMENT '消息类型，1-主动催办，2-系统触发',
                         `is_read` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否已读，0-否，1-是',
                         `is_del` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
                         `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                         `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                         `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                         `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                         PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='消息表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;
