insert into c_customer_service_period_month_tax_type_check
select null,ccspm.id, c.report_type,c.tax_type,'',now(),'',now() from c_customer_service ccs join c_customer_tax_type_check c
                                                                                                  on ccs.id = c.customer_service_id
                                                                                             join c_customer_service_period_month ccspm on ccs.id = ccspm.customer_service_id
order by ccspm.id,c.id;

insert into c_customer_service_period_month_tax_type_check
select null,ccspm.id, b.report_type,b.tax_type,'',now(),'',now() from (
                                                                          select ccs.* from c_customer_service ccs left join c_customer_tax_type_check c on ccs.id = c.customer_service_id
                                                                          where c.id is null and ccs.tax_type = 2) a join c_customer_tax_type_check b on b.customer_service_id = -1
                                                                                                                     join c_customer_service_period_month ccspm on a.id = ccspm.customer_service_id
order by ccspm.id, b.id;

insert into c_customer_service_period_month_tax_type_check
select null,ccspm.id, b.report_type,b.tax_type,'',now(),'',now() from (
                                                                          select ccs.* from c_customer_service ccs left join c_customer_tax_type_check c on ccs.id = c.customer_service_id
                                                                          where c.id is null and ccs.tax_type = 1) a join c_customer_tax_type_check b on b.customer_service_id = 0
                                                                                                                     join c_customer_service_period_month ccspm on a.id = ccspm.customer_service_id
order by ccspm.id, b.id;