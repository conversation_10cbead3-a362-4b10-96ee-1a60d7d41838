CREATE TABLE `sys_client_version` (
                                      `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                      `client_type` TINYINT(2) NOT NULL COMMENT '第三方客户端类型',
                                      `version_code` VARCHAR(50) NOT NULL COMMENT '当前最新版本',
                                      `download_url` VARCHAR(255) NOT NULL COMMENT '软件下载地址',
                                      `update_desc` VARCHAR(500) NULL DEFAULT NULL COMMENT '软件更新描述',
                                      `is_force_update` tinyint(1) not null default 0 comment '是否强制更新，0-否，1-是',
                                      `is_del` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
                                      `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                      `create_time` DATETIME NOT NULL DEFAULT (now()) COMMENT '创建时间',
                                      `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                      `update_time` DATETIME NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='第三方客户端版本表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;
CREATE TABLE `sys_usual_link` (
                                  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                  `link_type` TINYINT(2) NOT NULL COMMENT '链接类型，1-官方链接，2-内部链接',
                                  `dept_id` BIGINT(20) NULL DEFAULT NULL COMMENT '集团部门id',
                                  `link_name` VARCHAR(255) NOT NULL COMMENT '链接名称',
                                  `link_url` VARCHAR(255) NOT NULL COMMENT '链接' COLLATE 'utf8mb4_general_ci',
                                  `is_del` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
                                  `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                  `create_time` DATETIME NOT NULL DEFAULT (now()) COMMENT '创建时间',
                                  `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                  `update_time` DATETIME NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='常用链接表'
	COLLATE='utf8mb4_general_ci'
	ENGINE=InnoDB
;
