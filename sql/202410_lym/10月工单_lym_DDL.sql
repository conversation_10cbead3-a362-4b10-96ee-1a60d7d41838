CREATE TABLE `c_work_order` (
                                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                `customer_service_id` BIGINT NULL DEFAULT NULL COMMENT '客户服务id',
                                `title` VARCHAR(500) NOT NULL COMMENT '工单标题',
                                `work_order_type` TINYINT(2) NOT NULL COMMENT '工单类型，1-装订账本，2-找材料，3-对账，4-申报更正，5-申报表，6-做账要求，7-统计资料，8-催账，9-催库存，10-添加实名人员，11-补录初期，12-材料补充，13-账务问题，14-其他提醒',
                                `remark` VARCHAR(500) NULL DEFAULT NULL COMMENT '备注',
                                `ddl` DATE NULL DEFAULT NULL COMMENT 'DDL',
                                `initiate_user_id` BIGINT NOT NULL COMMENT '发起用户id',
                                `initiate_user_nickname` VARCHAR(255) NULL DEFAULT NULL COMMENT '发起用户昵称',
                                `initiate_employee_id` BIGINT NULL DEFAULT NULL COMMENT '发起员工id',
                                `initiate_employee_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '发起员工名称',
                                `initiate_dept_id` BIGINT NOT NULL COMMENT '发起小组id',
                                `undertake_user_id` BIGINT NULL DEFAULT NULL COMMENT '承接用户id',
                                `undertake_user_nickname` VARCHAR(255) NULL DEFAULT NULL COMMENT '承接用户昵称',
                                `undertake_employee_id` BIGINT NULL DEFAULT NULL COMMENT '承接员工id',
                                `undertake_employee_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '承接员工名称',
                                `undertake_dept_id` BIGINT NULL DEFAULT NULL COMMENT '承接小组id',
                                `current_user_type` TINYINT(2) NULL DEFAULT NULL COMMENT '当前处理人类型，1-发起人，2-承接人',
                                `current_user_id` BIGINT NULL DEFAULT NULL COMMENT '当前处理用户id',
                                `current_user_nickname` VARCHAR(255) NULL DEFAULT NULL COMMENT '当前处理用户昵称',
                                `current_employee_id` BIGINT NULL DEFAULT NULL COMMENT '当前处理员工id',
                                `current_employee_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '当前处理员工名称',
                                `current_dept_id` BIGINT NULL DEFAULT NULL COMMENT '当前处理小组id',
                                `status` TINYINT(2) NOT NULL DEFAULT 1 COMMENT '状态，1-待完结，2-已完结',
                                `last_follow_up_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '最近跟进人',
                                `last_follow_up_time` DATETIME NULL DEFAULT NULL COMMENT '最近跟进时间',
                                `last_oper_name` VARCHAR(255) NULL DEFAULT NULL COMMENT '最近操作人',
                                `last_oper_type` VARCHAR(255) NULL DEFAULT NULL COMMENT '最近操作类型',
                                `last_oper_time` DATETIME NULL DEFAULT NULL COMMENT '最近操作时间',
                                `is_del` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0-否，1-是',
                                `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='工单表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `c_work_order_file` (
                                     `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                     `work_order_id` BIGINT NOT NULL COMMENT '工单id',
                                     `file_url` VARCHAR(500) NULL DEFAULT NULL COMMENT '文件地址' COLLATE 'utf8mb4_general_ci',
                                     `file_name` VARCHAR(500) NULL DEFAULT NULL COMMENT '文件名称' COLLATE 'utf8mb4_general_ci',
                                     `file_type` TINYINT NOT NULL COMMENT '附件类型，1-创建附件',
                                     `is_del` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
                                     `create_by` VARCHAR(64) NULL DEFAULT '' COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
                                     `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
                                     `update_by` VARCHAR(64) NULL DEFAULT '' COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
                                     `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE
)
    COMMENT='工单附件表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;