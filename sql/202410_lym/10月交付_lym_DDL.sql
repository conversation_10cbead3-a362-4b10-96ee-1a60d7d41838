ALTER TABLE c_customer_deliver
    ADD COLUMN `pre_auth_info` JSON NULL DEFAULT NULL COMMENT '预认证数据' AFTER `auth_remark`,
    ADD COLUMN `pre_auth_confirm_remark` VARCHAR(1000) NULL DEFAULT NULL COMMENT '预认证确认备注' AFTER `pre_auth_info`,
    ADD COLUMN `pre_auth_confirm_result` TINYINT(2) NULL DEFAULT NULL COMMENT '预认证确认结果，1-确认，2-驳回' AFTER `pre_auth_confirm_remark`;
ALTER TABLE c_customer_deliver
    ADD COLUMN `last_oper_type` VARCHAR(100) NULL DEFAULT NULL COMMENT '最后一次操作记录类型' AFTER `has_changed`,
	ADD COLUMN `last_oper_name` VARCHAR(50) NULL DEFAULT '' COMMENT '最后一次操作人员' AFTER `last_oper_type`,
	ADD COLUMN `last_oper_time` DATETIME NULL DEFAULT NULL COMMENT '最后一次操作时间' AFTER `last_oper_name`;
-- ALTER TABLE c_customer_service_period_month_income
--     ADD COLUMN `total_invoice_amount` DECIMAL(40,20) NULL DEFAULT NULL COMMENT '全量取得发票金额',
--     ADD COLUMN `total_invoice_tax_amount` DECIMAL(40,20) NULL DEFAULT NULL COMMENT '全量取得发票税额';
-- ALTER TABLE c_customer_service_period_year ADD COLUMN last_year_deductible VARCHAR(50) NULL DEFAULT NULL COMMENT '上年可弥补损益' AFTER `period`;
ALTER TABLE `c_open_api_sync_customer`
    ADD COLUMN `pre_auth_result` VARCHAR(500) NULL DEFAULT NULL COMMENT '预认证处理结果' AFTER `social_security_deliver_id`,
	ADD COLUMN `pre_auth_deliver_id` BIGINT NULL DEFAULT NULL COMMENT '预认证交付单id' AFTER `pre_auth_result`;

CREATE TABLE `c_open_api_sync_vat_data` (
            `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `syc_record_id` BIGINT NOT NULL COMMENT '同步记录id',
            `sync_customer_id` BIGINT NOT NULL COMMENT '同步客户id',
            `customer_id` VARCHAR(255) COMMENT '客户ID',
            `customer_code` VARCHAR(255) COMMENT '客户编号',
            `customer_name` VARCHAR(255) COMMENT '客户名称',
            `tax_number` VARCHAR(255) COMMENT '税号',
            `taxpayer_qualification` VARCHAR(255) COMMENT '纳税人资格',
            `is_tax_reminder_sent` VARCHAR(255) COMMENT '是否已发送纳税提醒',
            `period_start` VARCHAR(255) COMMENT '所属期起',
            `period_end` VARCHAR(255) COMMENT '所属期止',
            `last_period_carried_forward_tax` VARCHAR(255) COMMENT '上期留抵税额',
            `monthly_sales_amount` VARCHAR(255) COMMENT '本月(季)销售金额',
            `un_invoiced_income` VARCHAR(255) COMMENT '其中：无票收入',
            `current_output_tax` VARCHAR(255) COMMENT '本期销项税额',
            `current_simple_tax_collection` VARCHAR(255) COMMENT '本期简易征收税额',
            `current_certified_input_tax` VARCHAR(255) COMMENT '本期已认证进项税额',
            `current_pending_input_tax` VARCHAR(255) COMMENT '本期待确认进项税额',
            `current_selected_input_tax` VARCHAR(255) COMMENT '本期已勾选进项税额',
            `pending_input_tax_for_calculation` VARCHAR(255) COMMENT '用于计税的待确认进项范围',
            `current_selected_and_certified_input_tax` VARCHAR(255) COMMENT '本期已勾选并确认的进项税额',
            `current_red_letter_input_tax_should_revert` VARCHAR(255) COMMENT '本期红字进项应转出税额',
            `current_expected_payable_vat` VARCHAR(255) COMMENT '本期预计应缴增值税',
            `current_paid_vat` VARCHAR(255) COMMENT '本期已缴增值税',
            `current_actual_reported_input_tax` VARCHAR(255) COMMENT '本期实际申报认证进项税额',
            `current_expected_remaining_input_tax` VARCHAR(255) COMMENT '本期预计结余可用进项税额',
            `current_invoice_count` VARCHAR(255) COMMENT '本期开票张数',
            `vat_burden_rate_last_period` VARCHAR(255) COMMENT '截止上期本年增值税税负率',
            `vat_burden_rate_last_year` VARCHAR(255) COMMENT '上年增值税税负率',
            `cumulative_vat_paid_last_period` VARCHAR(255) COMMENT '截止上期本年累计缴纳增值税',
            `cumulative_accelerated_additional_input_tax_last_month` VARCHAR(255) COMMENT '截止上月本年累计加速加计扣除进项税额',
            `cumulative_turnover_last_period` VARCHAR(255) COMMENT '截止上期本年累计营业额',
            `cumulative_turnover_current_month` VARCHAR(255) COMMENT '截止本月本年累计营业额',
            `cumulative_output_tax_current_month` VARCHAR(255) COMMENT '截止本月本年累计销项税额',
            `cumulative_accelerated_additional_input_tax_current_month` VARCHAR(255) COMMENT '截止本月本年累计加速加计扣除进项税额',
            `cumulative_expected_vat_current_month` VARCHAR(255) COMMENT '截止本月本年累计预计缴纳增值税额',
            `estimated_vat_burden_rate_current_month` VARCHAR(255) COMMENT '截止本月预计增值税税负率',
            `cumulative_actual_deducted_input_tax_current_month` VARCHAR(255) COMMENT '截止本月本年累计实际抵扣进项税额',
            `cumulative_input_invoice_amount_excluding_tax_current_month` VARCHAR(255) COMMENT '截止本月本年累计进项发票金额（不含税）',
            `cumulative_input_invoice_amount_including_tax_current_month` VARCHAR(255) COMMENT '截止本月本年累计进项发票金额（含税）',
            `cumulative_invoiced_amount_last_12_months` VARCHAR(255) COMMENT '近12个月累计开票金额',
            `is_general_taxpayer` VARCHAR(255) COMMENT '是否一般纳税人',
            `is_input_output_updated` VARCHAR(255) COMMENT '进销项是否更新',
            `is_last_period_vat_form_downloaded` VARCHAR(255) COMMENT '是否下载上期增值税申报表',
            `current_certified_input_amount` VARCHAR(255) COMMENT '本期已认证进项金额',
            `current_pending_input_amount` VARCHAR(255) COMMENT '本期待确认进项金额',
            `current_selected_and_certified_input_amount` VARCHAR(255) COMMENT '本期已勾选并确认的进项金额',
            `function_staff` VARCHAR(255) COMMENT '职能人员',
            `other_function_staff` VARCHAR(255) COMMENT '其他职能人员',
            `vat_composite_tax_rate_setting` VARCHAR(255) COMMENT '增值税销项综合税率设置',
            `planned_vat_burden_rate` VARCHAR(255) COMMENT '增值税计划税负率',
            `current_max_invoice_limit_amount` VARCHAR(255) COMMENT '本期当前可开票上限总金额(含税)',
            `current_remaining_invoice_limit_amount` VARCHAR(255) COMMENT '本期当前剩余可开票上限金额(含税)',
            `current_suggested_vat_payment` VARCHAR(255) COMMENT '本期当前应当或建议缴纳税额',
            `current_suggested_certified_input_tax` VARCHAR(255) COMMENT '本期当前应当或建议认证进项税额',
            `current_suggested_additional_input_tax_amount` VARCHAR(255) COMMENT '本期当前建议补充含税进项金额',
            `vat_due_after_additional_input` VARCHAR(255) COMMENT '补充进项后当前应纳税额',
            `last_download_date` VARCHAR(255) COMMENT '最近下载日期',
            `last_month_download_date` VARCHAR(255) COMMENT '所属月最后下载日期',
            `is_vat_declared_this_period` VARCHAR(255) COMMENT '本期增值税是否申报',
            `permanent_remark` VARCHAR(255) COMMENT '永久备注',
            `current_remark` VARCHAR(255) COMMENT '本期备注',
            `create_by` VARCHAR(64) NULL DEFAULT NULL COMMENT '创建者' COLLATE 'utf8mb4_general_ci',
            `create_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) COMMENT '创建时间',
            `update_by` VARCHAR(64) NULL DEFAULT NULL COMMENT '更新者' COLLATE 'utf8mb4_general_ci',
            `update_time` DATETIME NOT NULL DEFAULT (CURRENT_TIMESTAMP) ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`) USING BTREE
)
COMMENT='第三方增值税涉税分析查询结果表'
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;
