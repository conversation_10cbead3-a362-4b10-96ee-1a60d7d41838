-- 为增值交付单表添加客户服务ID字段
-- 用于支持"改账"场景的客户服务关联

-- 添加 custom_id 字段
ALTER TABLE `c_value_added_delivery_order` 
ADD COLUMN `custom_id` BIGINT NULL COMMENT '客户服务ID（改账场景使用）' AFTER `business_top_dept_id`;

-- 添加索引以提高查询性能
CREATE INDEX `idx_custom_id` ON `c_value_added_delivery_order` (`custom_id`);

-- 添加外键约束（可选，根据业务需求决定是否启用）
-- ALTER TABLE `c_value_added_delivery_order` 
-- ADD CONSTRAINT `fk_value_added_delivery_order_custom_id` 
-- FOREIGN KEY (`custom_id`) REFERENCES `c_customer_service` (`id`) 
-- ON DELETE SET NULL ON UPDATE CASCADE;
