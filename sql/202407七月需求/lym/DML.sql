INSERT INTO c_customer_deliver_template (deliver_type, oper_type, file_url, create_by, create_time, update_by, update_time) VALUES (7, 10, 'template/inAccountTemplate_1.0.0.xlsx', '', now(), '', now());
INSERT INTO c_customer_deliver_template (deliver_type, oper_type, file_url, create_by, create_time, update_by, update_time) VALUES (1, 11, 'template/confirmTemplate_1.0.0.xlsx', '', now(), '', now());
INSERT INTO c_customer_deliver_template (deliver_type, oper_type, file_url, create_by, create_time, update_by, update_time) VALUES (2, 11, 'template/confirmTemplate_1.0.0.xlsx', '', now(), '', now());
INSERT INTO c_customer_deliver_template (deliver_type, oper_type, file_url, create_by, create_time, update_by, update_time) VALUES (3, 11, 'template/confirmTemplate_1.0.0.xlsx', '', now(), '', now());
INSERT INTO c_customer_deliver_template (deliver_type, oper_type, file_url, create_by, create_time, update_by, update_time) VALUES (4, 11, 'template/confirmTemplate_1.0.0.xlsx', '', now(), '', now());
INSERT INTO c_customer_deliver_template (deliver_type, oper_type, file_url, create_by, create_time, update_by, update_time) VALUES (6, 11, 'template/confirmTemplate_1.0.0.xlsx', '', now(), '', now());

INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, is_frame, is_cache, menu_type, visible, status, perms) VALUES
                                                                                                                                 (2120,'材料交接统计', 0, 1, '', 1, 0, 'F', '0', '0', 'customer:workBench:docHandoverStatistic'),
                                                                                                                                 (2121,'入账统计', 0, 1, '', 1, 0, 'F', '0', '0', 'customer:workBench:inAccountDocHandoverStatistic'),
                                                                                                                                 (2122,'入账miniList', 0, 1, '', 1, 0, 'F', '0', '0', 'customer:inAccount:miniList'),
                                                                                                                                 (2123,'年度汇总', 2017, 1, '', 1, 0, 'F', '0', '0', 'customer:customerService:periodYearList'),
                                                                                                                                 (2124,'年度汇总-明细下载', 2017, 1, '', 1, 0, 'F', '0', '0', 'customer:customerService:periodYearExport'),
                                                                                                                                 (2125,'收入-新建', 2033, 1, '', 1, 0, 'F', '0', '0', 'customer:income:add'),
                                                                                                                                 (2126,'收入-编辑', 2033, 1, '', 1, 0, 'F', '0', '0', 'customer:income:edit'),
                                                                                                                                 (2127,'入账列表', 2016, 1, '', 1, 0, 'F', '0', '0', 'customer:inAccount:list'),
                                                                                                                                 (2128,'入账-编辑', 2117, 1, '', 1, 0, 'F', '0', '0', 'customer:inAccount:updateInAccount'),
                                                                                                                                 (2129,'入账-批量入账', 2117, 1, '', 1, 0, 'F', '0', '0', 'customer:inAccount:inAccountInBatch'),
                                                                                                                                 (2130,'入账-批量交付', 2117, 1, '', 1, 0, 'F', '0', '0', 'file:deliver:inAccountBatchDeliver'),
                                                                                                                                 (2131,'材料', 0, 1, 'customer/docHandover', 1, 0, 'M', '0', '0', null),
                                                                                                                                 (2132,'材料列表', 2121, 1, '', 1, 0, 'F', '0', '0', 'customer:handover:docHandoverList'),
                                                                                                                                 (2133,'材料-新建', 2121, 1, '', 1, 0, 'F', '0', '0', 'customer:handover:add'),
                                                                                                                                 (2134,'材料-核验', 2121, 1, '', 1, 0, 'F', '0', '0', 'customer:handover:check'),
                                                                                                                                 (2135,'材料-退回', 2121, 1, '', 1, 0, 'F', '0', '0', 'customer:handover:reBack'),
                                                                                                                                 (2136,'材料-承验转交', 2121, 1, '', 1, 0, 'F', '0', '0', 'customer:handover:change'),
                                                                                                                                 (2137,'批量交付-确认', 2032, 1, '', 1, 0, 'F', '0', '0', 'file:deliverBatch:confirmDeliver'),
                                                                                                                                 (2138,'分派-全部生效', 2017, 1, '', 1, 0, 'F', '0', '0', 'customer:dispatch:all'),
                                                                                                                                 (2139,'补账-数字统计', 0, 1, '', 1, 0, 'F', '0', '0', 'customer:repairAccount:statistic');
INSERT INTO sys_role (role_id, role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, update_by, update_time, remark) VALUES
                                                                                                                                                                                                   (107, '流程助理', 'flowAssistant', 0, '1', 0, 1, '0', '0', 'admin', '2024-05-26 23:16:04', '', null, null),
                                                                                                                                                                                                   (108, '档案管理', 'filesManager', 0, '1', 0, 1, '0', '0', 'admin', '2024-05-26 23:16:04', '', null, null);

###未执行
insert into sys_role_menu (menu_id, role_id) values
                                                 (2120, 100),(2120, 102),(2120, 103),(2120, 108),(2120, 109),(2121, 100),(2121, 102),(2121, 103),(2121, 101),(2121, 104),(2121, 105),(2122, 100),(2122, 102),(2122, 103),(2122, 101),(2122, 104),(2122, 105),
                                                 (2127, 100),(2127, 102),(2127, 103),(2127, 101),(2127, 104),(2127, 105),
                                                 (2128, 101),(2128, 104),(2128, 105),(2129, 101),(2129, 104),(2129, 105),
                                                 (2131, 100),(2131, 102),(2131, 103),(2131, 101),(2131, 104),(2131, 105),(2131, 108),(2131, 109),
                                                 (2132, 100),(2132, 102),(2132, 103),(2132, 101),(2132, 104),(2132, 105),(2132, 108),(2132, 109),
                                                 (2133, 100),(2133, 102),(2133, 103),(2133, 108),(2134, 101),(2134, 104),(2134, 105),(2134, 109),
                                                 (2135, 101),(2135, 104),(2135, 105),(2135, 109),(2136, 101),(2136, 104),(2136, 105),(2136, 109);

###已经执行
insert into sys_role_menu (menu_id, role_id) values
                                                 (2029, 109),
                                                 (2123, 100),(2123, 102),(2123, 103),(2123, 101),(2123, 104),(2123, 105),(2124, 102),(2124, 103),(2124, 104),(2124, 105),
                                                 (2126, 100),(2126, 102),(2126, 103),(2126, 101),(2126, 104),(2126, 105),(2137, 100),(2137, 103);

delete from sys_dept_menu;

insert into sys_dept_menu
select dept_id, menu_id
from sys_dept left join sys_menu on 1 = 1
where level = 2 and sys_dept.is_headquarters = 0 and menu_id >= 2000;


## 以下要根据实际dept_id进行调整
insert into sys_dept_menu (dept_id,menu_id) values
                                                (72, 2120),(72, 2129),(72, 2131),(72, 2132),(72, 2134),(72, 2135),(72, 2136),(72, 2000);
