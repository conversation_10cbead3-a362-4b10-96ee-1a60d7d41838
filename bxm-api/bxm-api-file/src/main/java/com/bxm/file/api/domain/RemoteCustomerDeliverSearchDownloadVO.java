package com.bxm.file.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteCustomerDeliverSearchDownloadVO {

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("标签名称搜索")
    private String tagName;

    @ApiModelProperty("标签是否包含，1-包含，0-不包含")
    private Integer tagIncludeFlag;

    @ApiModelProperty("账期开始时间，yyyyMM")
    private Integer periodStart;

    @ApiModelProperty("账期结束时间，yyyyMM")
    private Integer periodEnd;

    @ApiModelProperty("提交人")
    private String employeeName;

    @ApiModelProperty("提交时间-开始，yyyy-MM-dd")
    private String submitDateStart;

    @ApiModelProperty("提交时间-结束，yyyy-MM-dd")
    private String submitDateEnd;

    @ApiModelProperty("交付tab类型，2-医社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得）")
    private Integer tabType;

    @ApiModelProperty("交付类型搜索，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得）")
    private Integer deliverType;

    @ApiModelProperty("是否有人员变更，1-有，0-无")
    private Integer hasPersonChange;

    @ApiModelProperty("状态，0-已提交待申报，1-已保存待提交，2-已申报待确认，3-已确认待扣款，4-扣款已保存待提交，5-已扣款，6-待重提，7-申报异常，8-暂不申报，9-扣款异常，10-暂不扣款，101-已提交待补充，102-已补充待认证，103-已认证待确认，104-已确认，105-待重提，106-认证异常，107-暂不认证")
    private Integer status;

    @ApiModelProperty("金额筛选最小值")
    private BigDecimal reportAmountMin;

    @ApiModelProperty("金额筛选最大值")
    private BigDecimal reportAmountMax;

    @ApiModelProperty("导出附件类型，1-创建附件，2-申报附件，3-扣款附件，5-提报附件，6-认证附件，9-检查附件，多个逗号隔开")
    private String downloadFileTypes;

    @ApiModelProperty("顾问小组id")
    private Long advisorDeptId;

    @ApiModelProperty("会计小组id")
    private Long accountingDeptId;

    @ApiModelProperty("税种")
    private String taxCheckType;

    private Long deptId;

    private Long userId;

    private String downloadRecordTitle;

    private Long downloadRecordId;
}
