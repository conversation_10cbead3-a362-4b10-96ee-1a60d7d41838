package com.bxm.system.api.domain;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务统一操作记录对象 sys_business_log
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Data
@Accessors(chain = true)
public class BusinessLogDTO
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 业务类型 */
    private Integer businessType;

    /** 业务id */
    private Long businessId;

    /** 操作类型，直接存要展示的文字，否则枚举太多太乱 */
    private String operType;

    /** 操作用户id */
    private Long operUserId;

    /** 操作人员 */
    private String operName;

    /** 部门名称 */
    private String deptName;

    /** 操作内容 */
    private String operContent;

    /** 操作备注 */
    private String operRemark;

    /** 操作图片 */
    private String operImages;

    private Long deptId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    private String createBy;

    private List<CommonFileVO> files;
}
