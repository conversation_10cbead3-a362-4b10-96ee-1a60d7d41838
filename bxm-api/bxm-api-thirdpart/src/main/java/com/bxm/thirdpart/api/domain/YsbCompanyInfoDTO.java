package com.bxm.thirdpart.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YsbCompanyInfoDTO {

    @ApiModelProperty("注册地址")
    private String zcdz;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("注册时间")
    private String kyslrq;

    @ApiModelProperty("纳税人性质")
    private String nsrgm;

    @ApiModelProperty("生效期起")
    private String yxqq;
}
