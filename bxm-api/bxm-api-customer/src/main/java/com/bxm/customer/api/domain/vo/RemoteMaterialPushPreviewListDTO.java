package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteMaterialPushPreviewListDTO {
    @ApiModelProperty("交接单编号")
    @Excel(name = "交接单编号")
    private String materialDeliverNumber;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("银行信息")
    @Excel(name = "银行")
    private String bankInfo;

    @ApiModelProperty("账期")
    @Excel(name = "账期")
    private String period;

    @ApiModelProperty("文件")
    private List<CommonFileVO> files;

    @ApiModelProperty("附件数量")
    @Excel(name = "文件")
    private Long fileCount;

    @ApiModelProperty("失败原因")
    @Excel(name = "失败原因")
    private String errorMsg;
}
