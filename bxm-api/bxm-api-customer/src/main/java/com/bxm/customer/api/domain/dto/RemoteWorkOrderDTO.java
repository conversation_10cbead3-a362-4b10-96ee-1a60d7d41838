package com.bxm.customer.api.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteWorkOrderDTO {

    @ApiModelProperty("工单id")
    private Long id;

    @ApiModelProperty("工单类型，1-装订账本，2-找材料，3-对账，4-申报更正，5-申报表，6-做账要求，7-统计资料，8-催账，9-催库存，10-添加实名人员，11-补录初期，12-材料补充，13-账务问题，14-其他提醒，15-系统需求,16-改账")
    private Integer workOrderType;

    @ApiModelProperty("工单类型")
    @Excel(name = "工单类型")
    private String workOrderTypeStr;

    @ApiModelProperty("工单标题")
    @Excel(name = "工单标题")
    private String title;

    @ApiModelProperty("客户id")
    private Long customerServiceId;

    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerName;

    @Excel(name = "业务公司")
    private String businessDeptName;

    @Excel(name = "顾问小组")
    private String advisorDeptName;

    @Excel(name = "顾问")
    private String advisorEmployeeName;

    @Excel(name = "会计区域")
    private String accountingTopDeptName;

    @Excel(name = "会计小组")
    private String accountingDeptName;

    @Excel(name = "会计")
    private String accountingEmployeeName;

    @ApiModelProperty("开始账期")
    @Excel(name = "开始账期")
    private Integer periodStart;

    @ApiModelProperty("结束账期")
    @Excel(name = "结束账期")
    private Integer periodEnd;

    @ApiModelProperty("账期数")
    @Excel(name = "账期数")
    private Integer periodCount;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remark;

    @ApiModelProperty("附件数量")
    @Excel(name = "附件")
    private Integer fileCount;

    @ApiModelProperty("发起人")
    @Excel(name = "发起人")
    private String initiateUserNickname;

    @ApiModelProperty("发起人用户id")
    private Long initiateUserId;

    @ApiModelProperty("发起人小组id")
    private Long initiateDeptId;

    @ApiModelProperty("创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty("发起时间")
    @Excel(name = "发起时间")
    private String initiateTime;

    @ApiModelProperty("承接小组id")
    private Long undertakeDeptId;

    @ApiModelProperty("承接组")
    @Excel(name = "承接组")
    private String undertakeDeptName;

    @ApiModelProperty("承接人用户id")
    private Long undertakeUserId;

    @ApiModelProperty("承接人")
    @Excel(name = "承接人")
    private String undertakeUserNickname;

    @ApiModelProperty("状态，1-待完结，2-已完结")
    private Integer status;

    @ApiModelProperty("状态")
    @Excel(name = "状态")
    private String statusStr;

    @ApiModelProperty("DDL")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate ddl;

    @ApiModelProperty("DDL")
    @Excel(name = "DDL")
    private String ddlStr;

    @ApiModelProperty("完结时间")
    @Excel(name = "完结时间")
    private String finishTimeStr;

    @ApiModelProperty("最近操作人")
    @Excel(name = "最近操作人")
    private String lastOperName;

    @ApiModelProperty("最近操作")
    @Excel(name = "最近操作")
    private String lastOperType;

    @ApiModelProperty("最近操作时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastOperTime;

    @ApiModelProperty("最近操作时间")
    @Excel(name = "最近操作时间")
    private String lastOperTimeStr;

    @ApiModelProperty("当前处理人类型，1-发起人，2-承接人")
    private Integer currentUserType;

    @ApiModelProperty("当前处理人用户id")
    private Long currentUserId;

    @ApiModelProperty("当前处理人")
    private String currentUserNickname;

    @ApiModelProperty("当前处理人小组id")
    private Long currentDeptId;

    @ApiModelProperty("当前处理人小组名称")
    private String currentDeptName;

    @ApiModelProperty("当前处理人")
    @Excel(name = "当前处理人")
    private String currentUserInfo;

    @ApiModelProperty("当前用户能否跟进")
    private Boolean canFollowUp;

    @ApiModelProperty("当前用户能否转交发起方")
    private Boolean canTransmitInitiate;

    @ApiModelProperty("当前用户能否转交承接方")
    private Boolean canTransmitUndertake;

    @ApiModelProperty("当前用户能否确认")
    private Boolean canConfirm;

    private List<CommonFileVO> files;
}
