package com.bxm.customer.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteUpdateTaxReportTotalAmountVO {

    private Long id;

    private BigDecimal taxReportTotalAmount;

    private Long deptId;

    private Long userId;

    private String operName;
}
