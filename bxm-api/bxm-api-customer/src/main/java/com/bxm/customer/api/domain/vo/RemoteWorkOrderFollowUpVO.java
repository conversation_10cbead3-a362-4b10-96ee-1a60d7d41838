package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteWorkOrderFollowUpVO {

    @ApiModelProperty("工单id")
    private Long id;

    @ApiModelProperty("是否完结，0-否，1-是")
    private Integer isFinish;

    @ApiModelProperty("跟进类型，1-变更处理方，2-转交他人")
    private Integer followUpType;

    @ApiModelProperty("转交他人时选择的小组id")
    private Long deptId;

    @ApiModelProperty("转交他人时选择的员工id")
    private Long employeeId;

    @ApiModelProperty("ddl")
    private String ddl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    private Long userId;
}
