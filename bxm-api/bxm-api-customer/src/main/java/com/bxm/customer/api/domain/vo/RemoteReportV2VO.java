package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteReportV2VO {

    @ApiModelProperty("交付单id")
    private Long id;

    @ApiModelProperty(value = "申报结果，1-正常，2-异常")
    private Integer reportStatus;

    @ApiModelProperty(value = "本期金额")
    private BigDecimal currentPeriodAmount;

    @ApiModelProperty(value = "逾期金额")
    private BigDecimal overdueAmount;

    @ApiModelProperty(value = "补缴金额")
    private BigDecimal supplementAmount;

    @ApiModelProperty(hidden = true)
    private BigDecimal reportAmount;

    /** 申报备注 */
    @ApiModelProperty(value = "交付备注")
    private String reportRemark;

    @ApiModelProperty("汇算清缴标准附件")
    private List<CommonFileVO> huisuanqingjiaoFiles;

    @ApiModelProperty("已缴款标准附件")
    private List<CommonFileVO> yijiaokuanFiles;

    @ApiModelProperty("待缴款标准附件")
    private List<CommonFileVO> daijiaokuanFiles;

    @ApiModelProperty("其他交付附件")
    private List<CommonFileVO> otherDeliverFiles;

    @ApiModelProperty("1-保存，2-提交")
    private Integer saveType;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private String operName;

    @ApiModelProperty(hidden = true)
    private Boolean isCoverFiles;
}
